@model Technoloway.Web.Areas.Admin.Models.ChatbotResponseViewModel
@{
    ViewData["Title"] = "Edit Chatbot Response";
}

<div class="chatbot-admin">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-comments mr-2"></i>
                        Edit Chatbot Response
                    </h3>
                    <div class="card-tools">
                        <a href="@Url.Action("Responses", new { intentId = Model.ChatbotIntentId })" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Responses
                        </a>
                    </div>
                </div>
                <form asp-action="EditResponse" method="post">
                    <input asp-for="Id" type="hidden" />
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ResponseText" class="form-label"></label>
                                    <textarea asp-for="ResponseText" class="form-control" rows="4" placeholder="Enter response text"></textarea>
                                    <span asp-validation-for="ResponseText" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ChatbotIntentId" class="form-label"></label>
                                    <select asp-for="ChatbotIntentId" class="form-control">
                                        <option value="">Select an Intent</option>
                                        @foreach (var intent in ViewBag.Intents as IEnumerable<Technoloway.Core.Entities.ChatbotIntent>)
                                        {
                                            <option value="@intent.Id" selected="@(intent.Id == Model.ChatbotIntentId)">@intent.DisplayName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="ChatbotIntentId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>


                            <small class="form-text text-muted">
                                You can use HTML tags for formatting. Template variables: {{service_count}}, {{project_count}}, {{team_count}}, {{client_count}}
                            </small>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="ResponseType" class="form-label"></label>
                                    <select asp-for="ResponseType" class="form-control">
                                        <option value="text">Plain Text</option>
                                        <option value="html">HTML</option>
                                        <option value="dynamic">Dynamic</option>
                                    </select>
                                    <span asp-validation-for="ResponseType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DisplayOrder" class="form-label"></label>
                                    <input asp-for="DisplayOrder" class="form-control" type="number" min="0" />
                                    <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                                    <small class="form-text text-muted">Lower numbers appear first</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <div class="custom-control custom-switch mt-4">
                                        <input asp-for="IsActive" class="custom-control-input" type="checkbox" />
                                        <label asp-for="IsActive" class="custom-control-label"></label>
                                    </div>
                                    <small class="form-text text-muted">Only active responses are used</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Conditions" class="form-label"></label>
                                    <textarea asp-for="Conditions" class="form-control" rows="3" placeholder='{"time": "business_hours"}'></textarea>
                                    <span asp-validation-for="Conditions" class="text-danger"></span>
                                    <small class="form-text text-muted">JSON conditions for when to show this response (optional)</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="TemplateVariables" class="form-label"></label>
                                    <textarea asp-for="TemplateVariables" class="form-control" rows="3" placeholder='{"service_count": "dynamic"}'></textarea>
                                    <span asp-validation-for="TemplateVariables" class="text-danger"></span>
                                    <small class="form-text text-muted">JSON template variables configuration (optional)</small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle mr-1"></i> Template Variables</h6>
                            <p class="mb-2">You can use these dynamic variables in your content:</p>
                            <ul class="mb-0">
                                <li><code>{{service_count}}</code> - Number of active services</li>
                                <li><code>{{project_count}}</code> - Number of completed projects</li>
                                <li><code>{{team_count}}</code> - Number of team members</li>
                                <li><code>{{client_count}}</code> - Number of unique clients</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i>
                            Update Response
                        </button>
                        <a href="@Url.Action("Responses", new { intentId = Model.ChatbotIntentId })" class="btn btn-secondary">
                            <i class="fas fa-times mr-1"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        ClassicEditor
            .create(document.querySelector('#content-editor'), {
                toolbar: {
                    items: [
                        'heading', '|',
                        'bold', 'italic', 'underline', '|',
                        'bulletedList', 'numberedList', '|',
                        'link', 'blockQuote', '|',
                        'insertTable', '|',
                        'undo', 'redo'
                    ]
                },
                heading: {
                    options: [
                        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                        { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                    ]
                }
            })
            .catch(error => {
                console.error(error);
            });
    </script>
}
