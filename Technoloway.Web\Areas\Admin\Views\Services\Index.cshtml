@model Technoloway.Web.Areas.Admin.Models.ServiceManagementViewModel

@{
    ViewData["Title"] = "Service Management";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Service Management</h1>
            <p class="text-muted mb-0">Manage categories, services, options, and features</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary" onclick="showCategoryModal()">
                <i class="fas fa-plus"></i>
                Add Category
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add Service
            </a>
        </div>
    </div>

    <!-- Hierarchical Structure Info -->
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-3"></i>
            <div>
                <strong>Hierarchical Structure:</strong>
                <span class="ms-2">
                    <i class="fas fa-folder text-primary"></i> Categories
                    <i class="fas fa-arrow-right mx-2 text-muted"></i>
                    <i class="fas fa-server text-success"></i> Services
                    <i class="fas fa-arrow-right mx-2 text-muted"></i>
                    <i class="fas fa-cogs text-warning"></i> Options
                    <i class="fas fa-arrow-right mx-2 text-muted"></i>
                    <i class="fas fa-star text-info"></i> Features
                </span>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Categories</p>
                        <h3 class="admin-stat-number">@Model.Categories.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Services</p>
                        <h3 class="admin-stat-number">@Model.Services.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Service Options</p>
                        <h3 class="admin-stat-number">@Model.Services.Sum(s => s.ServiceOptions.Count)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Features</p>
                        <h3 class="admin-stat-number">@Model.Services.Sum(s => s.ServiceOptions.Sum(so => so.Features.Count))</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hierarchical Service Management -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">Service Hierarchy Management</h5>
                <p class="text-muted mb-0 small">Manage categories, services, options, and features in a hierarchical structure</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm" onclick="expandAll()">
                    <i class="fas fa-expand-arrows-alt me-1"></i>
                    Expand All
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="collapseAll()">
                    <i class="fas fa-compress-arrows-alt me-1"></i>
                    Collapse All
                </button>
                <button class="btn btn-primary btn-sm" onclick="showCategoryModal()">
                    <i class="fas fa-plus me-1"></i>
                    Add Category
                </button>
            </div>
        </div>
        <div class="card-body p-4">
            @if (Model.Categories.Any())
            {
                <div class="hierarchy-container">
                    @foreach (var category in Model.Categories.Where(c => c.ParentID == null).OrderBy(c => c.CategName))
                    {
                        <div class="category-section mb-4" data-category-id="@category.Id">
                            <!-- Category Header -->
                            <div class="hierarchy-item category-item">
                                <div class="d-flex align-items-center justify-content-between p-3 border rounded bg-light">
                                    <div class="d-flex align-items-center flex-grow-1">
                                        <button class="btn btn-sm btn-outline-secondary me-3 toggle-btn" onclick="toggleCategory(@category.Id)">
                                            <i class="fas fa-chevron-right" id="<EMAIL>"></i>
                                        </button>
                                        <div class="hierarchy-icon me-3">
                                            <i class="fas fa-folder text-primary"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 fw-bold text-primary">@category.CategName</h6>
                                            @if (!string.IsNullOrEmpty(category.CategDesc))
                                            {
                                                <p class="mb-0 text-muted small">@category.CategDesc</p>
                                            }
                                            <div class="mt-1">
                                                <span class="badge bg-primary">@category.Services.Count Services</span>
                                                <span class="badge bg-secondary ms-1">@category.Services.Sum(s => s.ServiceOptions.Count) Options</span>
                                                <span class="badge bg-info ms-1">@category.Services.Sum(s => s.ServiceOptions.Sum(so => so.Features.Count)) Features</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-outline-success btn-sm" onclick="addServiceToCategory(@category.Id)" title="Add Service">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" onclick="editCategory(@category.Id)" title="Edit Category">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteCategory(@category.Id)" title="Delete Category">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Services under this Category -->
                            <div class="category-content" id="<EMAIL>" style="display: none;">
                                @if (category.Services.Any())
                                {
                                    @foreach (var service in category.Services.OrderBy(s => s.DisplayOrder))
                                    {
                                        <div class="service-section ms-4 mt-3" data-service-id="@service.Id">
                                            <!-- Service Header -->
                                            <div class="hierarchy-item service-item">
                                                <div class="d-flex align-items-center justify-content-between p-3 border rounded bg-white">
                                                    <div class="d-flex align-items-center flex-grow-1">
                                                        <button class="btn btn-sm btn-outline-secondary me-3 toggle-btn" onclick="toggleService(@service.Id)">
                                                            <i class="fas fa-chevron-right" id="<EMAIL>"></i>
                                                        </button>
                                                        <div class="hierarchy-icon me-3">
                                                            @if (!string.IsNullOrEmpty(service.IconClass))
                                                            {
                                                                <i class="@service.IconClass text-success"></i>
                                                            }
                                                            else
                                                            {
                                                                <i class="fas fa-server text-success"></i>
                                                            }
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-1 fw-bold text-success">@service.Name</h6>
                                                            <p class="mb-0 text-muted small">@service.Description</p>
                                                            <div class="mt-1">
                                                                <span class="badge bg-success">@service.Price.ToString("C")</span>
                                                                @if (service.ServManager != null)
                                                                {
                                                                    <span class="badge bg-secondary ms-1">@service.ServManager</span>
                                                                }
                                                                <span class="badge bg-warning ms-1">@service.ServiceOptions.Count Options</span>
                                                                <span class="badge bg-info ms-1">@service.ServiceOptions.Sum(so => so.Features.Count) Features</span>
                                                                @if (service.IsActive)
                                                                {
                                                                    <span class="badge bg-success ms-1">Active</span>
                                                                }
                                                                else
                                                                {
                                                                    <span class="badge bg-secondary ms-1">Inactive</span>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex gap-1">
                                                        <button class="btn btn-outline-warning btn-sm" onclick="addServiceOption(@service.Id)" title="Add Option">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button class="btn btn-outline-primary btn-sm" onclick="editService(@service.Id)" title="Edit Service">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteService(@service.Id)" title="Delete Service">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                            <!-- Service Options under this Service -->
                            <div class="service-content ms-4" id="<EMAIL>" style="display: none;">
                                @if (service.ServiceOptions.Any())
                                {
                                    @foreach (var option in service.ServiceOptions.OrderBy(so => so.OptID))
                                    {
                                        <div class="option-section ms-4 mt-3" data-option-id="@option.Id">
                                            <!-- Service Option Header -->
                                            <div class="hierarchy-item option-item">
                                                <div class="d-flex align-items-center justify-content-between p-3 border rounded" style="background-color: #fff8e1;">
                                                    <div class="d-flex align-items-center flex-grow-1">
                                                        <button class="btn btn-sm btn-outline-secondary me-3 toggle-btn" onclick="toggleOption(@option.Id)">
                                                            <i class="fas fa-chevron-right" id="<EMAIL>"></i>
                                                        </button>
                                                        <div class="hierarchy-icon me-3">
                                                            <i class="fas fa-cogs text-warning"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-1 fw-bold text-warning">@option.OptName</h6>
                                                            @if (!string.IsNullOrEmpty(option.OptDesc))
                                                            {
                                                                <p class="mb-0 text-muted small">@option.OptDesc</p>
                                                            }
                                                            <div class="mt-1">
                                                                @if (option.OptCost.HasValue)
                                                                {
                                                                    <span class="badge bg-warning">@option.OptCost.Value.ToString("C")</span>
                                                                }
                                                                <span class="badge bg-secondary ms-1">@option.OptAvailability</span>
                                                                @if (option.OptDiscountRate.HasValue && option.OptDiscountRate > 0)
                                                                {
                                                                    <span class="badge bg-danger ms-1">@option.OptDiscountRate% Off</span>
                                                                }
                                                                <span class="badge bg-info ms-1">@option.Features.Count Features</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex gap-1">
                                                        <button class="btn btn-outline-info btn-sm" onclick="addServiceOptionFeature(@option.Id)" title="Add Feature">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button class="btn btn-outline-primary btn-sm" onclick="editServiceOption(@option.Id)" title="Edit Option">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteServiceOption(@option.Id)" title="Delete Option">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                            <!-- Service Option Features under this Option -->
                            <div class="option-content ms-4" id="<EMAIL>" style="display: none;">
                                @if (option.Features.Any())
                                {
                                    @foreach (var feature in option.Features.OrderBy(f => f.FeatID))
                                    {
                                        <div class="feature-section ms-4 mt-3" data-feature-id="@feature.Id">
                                            <!-- Service Option Feature Item -->
                                            <div class="hierarchy-item feature-item">
                                                <div class="d-flex align-items-center justify-content-between p-3 border rounded" style="background-color: #e3f2fd;">
                                                    <div class="d-flex align-items-center flex-grow-1">
                                                        <div class="hierarchy-icon me-3">
                                                            <i class="fas fa-star text-info"></i>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-1 fw-bold text-info">@feature.FeatName</h6>
                                                            @if (!string.IsNullOrEmpty(feature.FeatDesc))
                                                            {
                                                                <p class="mb-0 text-muted small">@feature.FeatDesc</p>
                                                            }
                                                            <div class="mt-1">
                                                                @if (feature.FeatCost.HasValue)
                                                                {
                                                                    <span class="badge bg-info">@feature.FeatCost.Value.ToString("C")</span>
                                                                }
                                                                <span class="badge bg-secondary ms-1">@feature.FeatAvailability</span>
                                                                @if (feature.FeatDiscountRate.HasValue && feature.FeatDiscountRate > 0)
                                                                {
                                                                    <span class="badge bg-danger ms-1">@feature.FeatDiscountRate% Off</span>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex gap-1">
                                                        <button class="btn btn-outline-primary btn-sm" onclick="editServiceOptionFeature(@feature.Id)" title="Edit Feature">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteServiceOptionFeature(@feature.Id)" title="Delete Feature">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <div class="ms-4 mt-3">
                                        <div class="text-center py-3 border rounded bg-light">
                                            <i class="fas fa-star text-muted mb-2" style="font-size: 2rem;"></i>
                                            <p class="text-muted mb-2">No features available for this option</p>
                                            <button class="btn btn-outline-info btn-sm" onclick="addServiceOptionFeature(@option.Id)">
                                                <i class="fas fa-plus me-1"></i>Add First Feature
                                            </button>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="ms-4 mt-3">
                        <div class="text-center py-3 border rounded bg-light">
                            <i class="fas fa-cogs text-muted mb-2" style="font-size: 2rem;"></i>
                            <p class="text-muted mb-2">No options available for this service</p>
                            <button class="btn btn-outline-warning btn-sm" onclick="addServiceOption(@service.Id)">
                                <i class="fas fa-plus me-1"></i>Add First Option
                            </button>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
}
else
{
    <div class="ms-4 mt-3">
        <div class="text-center py-3 border rounded bg-light">
            <i class="fas fa-server text-muted mb-2" style="font-size: 2rem;"></i>
            <p class="text-muted mb-2">No services available for this category</p>
            <button class="btn btn-outline-success btn-sm" onclick="addServiceToCategory(@category.Id)">
                <i class="fas fa-plus me-1"></i>Add First Service
            </button>
        </div>
    </div>
}
</div>
</div>
}

<!-- Subcategories -->
@foreach (var subcategory in Model.Categories.Where(c => c.ParentID == category.Id).OrderBy(c => c.CategName))
{
    <div class="subcategory-section ms-4 mt-3" data-category-id="@subcategory.Id">
        <!-- Subcategory Header -->
        <div class="hierarchy-item subcategory-item">
            <div class="d-flex align-items-center justify-content-between p-3 border rounded" style="background-color: #f8f9fa;">
                <div class="d-flex align-items-center flex-grow-1">
                    <button class="btn btn-sm btn-outline-secondary me-3 toggle-btn" onclick="toggleCategory(@subcategory.Id)">
                        <i class="fas fa-chevron-right" id="<EMAIL>"></i>
                    </button>
                    <div class="hierarchy-icon me-3">
                        <i class="fas fa-folder-open text-secondary"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1 fw-bold text-secondary">@subcategory.CategName</h6>
                        @if (!string.IsNullOrEmpty(subcategory.CategDesc))
                        {
                            <p class="mb-0 text-muted small">@subcategory.CategDesc</p>
                        }
                        <div class="mt-1">
                            <span class="badge bg-secondary">@subcategory.Services.Count Services</span>
                            <span class="badge bg-light text-dark ms-1">@subcategory.Services.Sum(s => s.ServiceOptions.Count) Options</span>
                            <span class="badge bg-light text-dark ms-1">@subcategory.Services.Sum(s => s.ServiceOptions.Sum(so => so.Features.Count)) Features</span>
                        </div>
                    </div>
                </div>
                <div class="d-flex gap-1">
                    <button class="btn btn-outline-success btn-sm" onclick="addServiceToCategory(@subcategory.Id)" title="Add Service">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="editCategory(@subcategory.Id)" title="Edit Category">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteCategory(@subcategory.Id)" title="Delete Category">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Services under this Subcategory (similar structure as main category) -->
        <div class="category-content" id="<EMAIL>" style="display: none;">
            <!-- Similar service structure as above for subcategory services -->
        </div>
    </div>
}
</div>
}
}
else
{
<div class="text-center py-5">
    <i class="fas fa-folder text-muted mb-3" style="font-size: 3rem;"></i>
    <h5 class="text-muted">No categories found</h5>
    <p class="text-muted">Create your first category to organize services.</p>
    <button class="btn btn-primary" onclick="showCategoryModal()">
        <i class="fas fa-plus me-2"></i>
        Create First Category
    </button>
</div>
}
</div>
</div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" id="categoryModalContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<!-- Service Option Modal -->
<div class="modal fade" id="serviceOptionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" id="serviceOptionModalContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<!-- Service Option Feature Modal -->
<div class="modal fade" id="serviceOptionFeatureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" id="serviceOptionFeatureModalContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add hover effects to stat cards
            $('.admin-stat-card').hover(
                function() { $(this).addClass('shadow-lg'); },
                function() { $(this).removeClass('shadow-lg'); }
            );

            // Add hover effects to hierarchy items
            $('.hierarchy-item').hover(
                function() { $(this).find('.border').addClass('shadow-sm'); },
                function() { $(this).find('.border').removeClass('shadow-sm'); }
            );
        });

        // Hierarchy Toggle Functions
        function toggleCategory(categoryId) {
            const content = $('#category-content-' + categoryId);
            const icon = $('#category-icon-' + categoryId);

            if (content.is(':visible')) {
                content.slideUp(300);
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
            } else {
                content.slideDown(300);
                icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
            }
        }

        function toggleService(serviceId) {
            const content = $('#service-content-' + serviceId);
            const icon = $('#service-icon-' + serviceId);

            if (content.is(':visible')) {
                content.slideUp(300);
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
            } else {
                content.slideDown(300);
                icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
            }
        }

        function toggleOption(optionId) {
            const content = $('#option-content-' + optionId);
            const icon = $('#option-icon-' + optionId);

            if (content.is(':visible')) {
                content.slideUp(300);
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
            } else {
                content.slideDown(300);
                icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
            }
        }

        function expandAll() {
            $('.category-content, .service-content, .option-content').slideDown(300);
            $('.toggle-btn i').removeClass('fa-chevron-right').addClass('fa-chevron-down');
        }

        function collapseAll() {
            $('.category-content, .service-content, .option-content').slideUp(300);
            $('.toggle-btn i').removeClass('fa-chevron-down').addClass('fa-chevron-right');
        }

        // Category Management Functions
        function showCategoryModal() {
            $.get('@Url.Action("CreateCategory")')
                .done(function(data) {
                    $('#categoryModalContent').html(data);
                    $('#categoryModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading category form.');
                });
        }

        function editCategory(id) {
            $.get('@Url.Action("EditCategory")/' + id)
                .done(function(data) {
                    $('#categoryModalContent').html(data);
                    $('#categoryModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading category form.');
                });
        }

        function deleteCategory(id) {
            if (confirm('Are you sure you want to delete this category?')) {
                $.post('@Url.Action("DeleteCategory")', { id: id, __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val() })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Error deleting category.');
                    });
            }
        }

        // Service Option Management Functions
        function addServiceOption(serviceId) {
            $.get('@Url.Action("CreateServiceOption")/' + serviceId)
                .done(function(data) {
                    $('#serviceOptionModalContent').html(data);
                    $('#serviceOptionModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading service option form.');
                });
        }

        function editServiceOption(id) {
            $.get('@Url.Action("EditServiceOption")/' + id)
                .done(function(data) {
                    $('#serviceOptionModalContent').html(data);
                    $('#serviceOptionModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading service option form.');
                });
        }

        function deleteServiceOption(id) {
            if (confirm('Are you sure you want to delete this service option?')) {
                $.post('@Url.Action("DeleteServiceOption")', { id: id, __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val() })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Error deleting service option.');
                    });
            }
        }

        // Service Option Feature Management Functions
        function addServiceOptionFeature(serviceOptionId) {
            $.get('@Url.Action("CreateServiceOptionFeature")/' + serviceOptionId)
                .done(function(data) {
                    $('#serviceOptionFeatureModalContent').html(data);
                    $('#serviceOptionFeatureModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading service option feature form.');
                });
        }

        function editServiceOptionFeature(id) {
            $.get('@Url.Action("EditServiceOptionFeature")/' + id)
                .done(function(data) {
                    $('#serviceOptionFeatureModalContent').html(data);
                    $('#serviceOptionFeatureModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading service option feature form.');
                });
        }

        function deleteServiceOptionFeature(id) {
            if (confirm('Are you sure you want to delete this service option feature?')) {
                $.post('@Url.Action("DeleteServiceOptionFeature")', { id: id, __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val() })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Error deleting service option feature.');
                    });
            }
        }

        function editService(id) {
            window.location.href = '@Url.Action("Edit")/' + id;
        }

        function addServiceToCategory(categoryId) {
            window.location.href = '@Url.Action("Create")?categoryId=' + categoryId;
        }

        function deleteService(id) {
            if (confirm('Are you sure you want to delete this service? This will also delete all associated options and features.')) {
                $.post('@Url.Action("Delete")', { id: id, __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val() })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Error deleting service.');
                    });
            }
        }

        // Form submission handlers
        $(document).on('submit', '#categoryForm', function(e) {
            e.preventDefault();
            var form = $(this);
            var url = form.attr('action');
            var formData = form.serialize();

            $.post(url, formData)
                .done(function(response) {
                    if (response.success) {
                        $('#categoryModal').modal('hide');
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function() {
                    alert('Error submitting form.');
                });
        });

        $(document).on('submit', '#serviceOptionForm', function(e) {
            e.preventDefault();
            var form = $(this);
            var url = form.attr('action');
            var formData = form.serialize();

            $.post(url, formData)
                .done(function(response) {
                    if (response.success) {
                        $('#serviceOptionModal').modal('hide');
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function() {
                    alert('Error submitting form.');
                });
        });

        $(document).on('submit', '#serviceOptionFeatureForm', function(e) {
            e.preventDefault();
            var form = $(this);
            var url = form.attr('action');
            var formData = form.serialize();

            $.post(url, formData)
                .done(function(response) {
                    if (response.success) {
                        $('#serviceOptionFeatureModal').modal('hide');
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function() {
                    alert('Error submitting form.');
                });
        });
    </script>
}

<style>
    .hierarchy-container {
        position: relative;
    }

    .hierarchy-item {
        position: relative;
        transition: all 0.3s ease;
    }

    .hierarchy-item:hover {
        transform: translateX(2px);
    }

    .hierarchy-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.8);
        border: 2px solid currentColor;
        font-size: 1.2rem;
    }

    .category-item .hierarchy-icon {
        background-color: rgba(13, 110, 253, 0.1);
        border-color: #0d6efd;
    }

    .service-item .hierarchy-icon {
        background-color: rgba(25, 135, 84, 0.1);
        border-color: #198754;
    }

    .option-item .hierarchy-icon {
        background-color: rgba(255, 193, 7, 0.1);
        border-color: #ffc107;
    }

    .feature-item .hierarchy-icon {
        background-color: rgba(13, 202, 240, 0.1);
        border-color: #0dcaf0;
    }

    .toggle-btn {
        transition: transform 0.3s ease;
        border: none !important;
        background: transparent !important;
    }

    .toggle-btn:hover {
        transform: scale(1.1);
        background: rgba(0, 0, 0, 0.05) !important;
    }

    .category-section {
        border-left: 4px solid #0d6efd;
        padding-left: 1rem;
        margin-left: 1rem;
    }

    .service-section {
        border-left: 4px solid #198754;
        padding-left: 1rem;
        margin-left: 1rem;
    }

    .option-section {
        border-left: 4px solid #ffc107;
        padding-left: 1rem;
        margin-left: 1rem;
    }

    .feature-section {
        border-left: 4px solid #0dcaf0;
        padding-left: 1rem;
        margin-left: 1rem;
    }

    .subcategory-section {
        border-left: 4px solid #6c757d;
        padding-left: 1rem;
        margin-left: 1rem;
    }

    .hierarchy-item .border {
        transition: all 0.3s ease;
    }

    .hierarchy-item:hover .border {
        border-color: #0d6efd !important;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }

    .btn-xs {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .category-content,
    .service-content,
    .option-content {
        animation: slideIn 0.3s ease when showing;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
    }

    .hierarchy-item h6 {
        margin-bottom: 0.25rem;
        font-size: 1rem;
    }

    .hierarchy-item p {
        font-size: 0.875rem;
        line-height: 1.4;
    }
</style>
