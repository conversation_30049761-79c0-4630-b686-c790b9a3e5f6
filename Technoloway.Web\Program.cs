using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Technoloway.Infrastructure;
using Technoloway.Infrastructure.Data;
using Technoloway.Infrastructure.Repositories;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Configuration;
using Technoloway.Web.Services;
using Stripe;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Infrastructure services setup
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") ??
    throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlite(connectionString);

    // Only enable sensitive data logging in development
    var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
    if (environment == "Development")
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
});

// Configure Identity
builder.Services.AddIdentity<IdentityUser, IdentityRole>(options =>
{
    options.SignIn.RequireConfirmedAccount = false; // Can be configured later
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequiredLength = 8;
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Authentication and authorization
builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Identity/Account/Login";
    options.LogoutPath = "/Identity/Account/Logout";
    options.AccessDeniedPath = "/Identity/Account/AccessDenied";
    options.SlidingExpiration = true;
    options.ExpireTimeSpan = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
});

// Repositories
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
builder.Services.AddScoped<ILegalPageRepository, LegalPageRepository>();
builder.Services.AddScoped<IAboutPageRepository, AboutPageRepository>();
builder.Services.AddScoped<IHeroSectionRepository, HeroSectionRepository>();

// Add MVC services
builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();

// Add custom services
builder.Services.AddScoped<IFileUploadService, FileUploadService>();
builder.Services.AddScoped<ISecureFileUploadService, SecureFileUploadService>();

// Configure Razor view engine for areas
builder.Services.Configure<Microsoft.AspNetCore.Mvc.Razor.RazorViewEngineOptions>(options =>
{
    Technoloway.Web.Areas.Admin.AdminAreaRegistration.RegisterArea(options);
    Technoloway.Web.Areas.Client.ClientAreaRegistration.RegisterArea(options);
});

// Configure authorization policies (consolidated)
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("RequireSuperAdmin", policy => policy.RequireRole("SuperAdmin"));
    options.AddPolicy("RequireContentManager", policy => policy.RequireRole("SuperAdmin", "ContentManager"));
    options.AddPolicy("RequireHRManager", policy => policy.RequireRole("SuperAdmin", "HRManager"));
    options.AddPolicy("RequireClient", policy => policy.RequireRole("Client"));
    options.AddPolicy("RequireAdmin", policy => policy.RequireRole("SuperAdmin", "ContentManager", "HRManager"));
    options.AddPolicy("RequireAdminRole", policy => policy.RequireRole("SuperAdmin", "ContentManager", "HRManager"));
});

// Configure Stripe
StripeConfiguration.ApiKey = builder.Configuration["Stripe:SecretKey"];

var app = builder.Build();

// Check for command line arguments
if (args.Length > 0 && args[0] == "--insert-categories")
{
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        await Technoloway.Web.InsertCategories.ExecuteAsync(context);
    }
    return;
}

if (args.Length > 0 && args[0] == "--fix-categories-schema")
{
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        await Technoloway.Web.FixCategoriesSchema.ExecuteAsync(context);
    }
    return;
}

if (args.Length > 0 && args[0] == "--insert-service-data")
{
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        await Technoloway.Web.InsertServiceData.ExecuteAsync(context);
    }
    return;
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseMigrationsEndPoint();
}
else
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.UseAuthentication();

// Custom middleware to handle admin area authentication redirects
app.Use(async (context, next) =>
{
    // If user is trying to access admin area and is not authenticated
    if (context.Request.Path.StartsWithSegments("/Admin") &&
        !context.User.Identity!.IsAuthenticated &&
        !context.Request.Path.StartsWithSegments("/Admin/Account/Login"))
    {
        // Redirect to admin login instead of default Identity login
        context.Response.Redirect($"/Admin/Account/Login?returnUrl={Uri.EscapeDataString(context.Request.Path + context.Request.QueryString)}");
        return;
    }
    await next(context);
});

app.UseAuthorization();

// Initialize the database
await DbInitializer.InitializeAsync(app.Services);

// Configure routes
app.MapControllerRoute(
    name: "admin",
    pattern: "Admin",
    defaults: new { area = "Admin", controller = "Home", action = "Index" });

app.MapControllerRoute(
    name: "adminLogin",
    pattern: "Admin/Login",
    defaults: new { area = "Admin", controller = "Account", action = "Login" });

// Redirect /Admin/Login to /Admin/Account/Login for convenience
app.Use(async (context, next) =>
{
    if (context.Request.Path.Value == "/Admin/Login")
    {
        context.Response.Redirect("/Admin/Account/Login");
        return;
    }
    await next(context);
});

// Custom routes for legal pages
app.MapControllerRoute(
    name: "terms",
    pattern: "terms",
    defaults: new { controller = "Home", action = "Terms" });

app.MapControllerRoute(
    name: "privacy",
    pattern: "privacy",
    defaults: new { controller = "Home", action = "Privacy" });

// Custom route for contact page
app.MapControllerRoute(
    name: "contact",
    pattern: "contact",
    defaults: new { controller = "Home", action = "Contact" });

// Custom route for about page
app.MapControllerRoute(
    name: "about",
    pattern: "about",
    defaults: new { controller = "Home", action = "About" });

app.MapControllerRoute(
    name: "areas",
    pattern: "{area:exists}/{controller=Home}/{action=Index}/{id?}");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapRazorPages();

app.Run();
