using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;

namespace Technoloway.Infrastructure.Data;

public class ApplicationDbContext : IdentityDbContext<IdentityUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<Service> Services { get; set; }
    public DbSet<Project> Projects { get; set; }
    public DbSet<Technology> Technologies { get; set; }
    public DbSet<TeamMember> TeamMembers { get; set; }
    public DbSet<BlogPost> BlogPosts { get; set; }
    public DbSet<JobListing> JobListings { get; set; }
    public DbSet<JobApplication> JobApplications { get; set; }
    public DbSet<Client> Clients { get; set; }
    public DbSet<Invoice> Invoices { get; set; }
    public DbSet<InvoiceItem> InvoiceItems { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<ProjectDocument> ProjectDocuments { get; set; }
    public DbSet<SiteSetting> SiteSettings { get; set; }
    public DbSet<Testimonial> Testimonials { get; set; }
    public DbSet<ContactForm> ContactForms { get; set; }
    public DbSet<Feedback> Feedbacks { get; set; }
    public DbSet<LegalPage> LegalPages { get; set; }
    public DbSet<LegalPageSection> LegalPageSections { get; set; }
    public DbSet<AboutPage> AboutPages { get; set; }
    public DbSet<AboutPageSection> AboutPageSections { get; set; }
    public DbSet<HeroSection> HeroSections { get; set; }

    // Chatbot entities
    public DbSet<ChatbotIntent> ChatbotIntents { get; set; }
    public DbSet<ChatbotResponse> ChatbotResponses { get; set; }
    public DbSet<ChatbotQuickAction> ChatbotQuickActions { get; set; }
    public DbSet<ChatbotKeyword> ChatbotKeywords { get; set; }
    public DbSet<HeroSlide> HeroSlides { get; set; }

    // New business entities
    public DbSet<Category> Categories { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderDetail> OrderDetails { get; set; }
    public DbSet<Contract> Contracts { get; set; }
    public DbSet<ServiceOption> ServiceOptions { get; set; }
    public DbSet<ServiceOptionFeature> ServiceOptionFeatures { get; set; }
    public DbSet<PayrollRecord> PayrollRecords { get; set; }
    public DbSet<ProjectTask> Tasks { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Configure relationships

        // Category relationships
        builder.Entity<Category>()
            .HasOne(c => c.Parent)
            .WithMany(c => c.Children)
            .HasForeignKey(c => c.ParentID)
            .OnDelete(DeleteBehavior.Restrict);

        // Service relationships
        builder.Entity<Service>()
            .HasOne(s => s.Category)
            .WithMany(c => c.Services)
            .HasForeignKey(s => s.CategID)
            .HasPrincipalKey(c => c.Id)
            .OnDelete(DeleteBehavior.Restrict);

        // Project relationships
        builder.Entity<Project>()
            .HasOne(p => p.Order)
            .WithMany(o => o.Projects)
            .HasForeignKey(p => p.OrderID)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Project>()
            .HasOne(p => p.Client)
            .WithMany(c => c.Projects)
            .HasForeignKey(p => p.ClientId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<Project>()
            .HasMany(p => p.Technologies)
            .WithMany(t => t.Projects)
            .UsingEntity(j => j.ToTable("ProjectTechnologies"));

        // Order relationships
        builder.Entity<Order>()
            .HasOne(o => o.Client)
            .WithMany(c => c.Orders)
            .HasForeignKey(o => o.ClientID)
            .OnDelete(DeleteBehavior.Restrict);

        // OrderDetail relationships
        builder.Entity<OrderDetail>()
            .HasOne(od => od.Order)
            .WithMany(o => o.OrderDetails)
            .HasForeignKey(od => od.OrderID)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<OrderDetail>()
            .HasOne(od => od.Service)
            .WithMany(s => s.OrderDetails)
            .HasForeignKey(od => od.ServID)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<OrderDetail>()
            .HasOne(od => od.ServiceOption)
            .WithMany(so => so.OrderDetails)
            .HasForeignKey(od => od.OptID)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<OrderDetail>()
            .HasOne(od => od.ServiceOptionFeature)
            .WithMany(sof => sof.OrderDetails)
            .HasForeignKey(od => od.FeatID)
            .OnDelete(DeleteBehavior.SetNull);

        // Contract relationships
        builder.Entity<Contract>()
            .HasOne(c => c.Project)
            .WithMany(p => p.Contracts)
            .HasForeignKey(c => c.ProjID)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Contract>()
            .HasOne(c => c.Client)
            .WithMany(cl => cl.Contracts)
            .HasForeignKey(c => c.ClientID)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Contract>()
            .HasOne(c => c.Order)
            .WithMany(o => o.Contracts)
            .HasForeignKey(c => c.OrderID)
            .OnDelete(DeleteBehavior.Restrict);

        // Invoice relationships
        builder.Entity<Invoice>()
            .HasOne(i => i.Client)
            .WithMany(c => c.Invoices)
            .HasForeignKey(i => i.ClientId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Invoice>()
            .HasOne(i => i.Contract)
            .WithMany(c => c.Invoices)
            .HasForeignKey(i => i.ContID)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Invoice>()
            .HasOne(i => i.Order)
            .WithMany(o => o.Invoices)
            .HasForeignKey(i => i.OrderID)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Payment>()
            .HasOne(p => p.Invoice)
            .WithMany(i => i.Payments)
            .HasForeignKey(p => p.InvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<InvoiceItem>()
            .HasOne(ii => ii.Invoice)
            .WithMany(i => i.Items)
            .HasForeignKey(ii => ii.InvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<Message>()
            .HasOne(m => m.Project)
            .WithMany(p => p.Messages)
            .HasForeignKey(m => m.ProjectId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ProjectDocument>()
            .HasOne(pd => pd.Project)
            .WithMany(p => p.Documents)
            .HasForeignKey(pd => pd.ProjectId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<JobApplication>()
            .HasOne(ja => ja.JobListing)
            .WithMany(jl => jl.Applications)
            .HasForeignKey(ja => ja.JobListingId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure SiteSetting Icon field as optional for Entity Framework validation
        builder.Entity<SiteSetting>()
            .Property(s => s.Icon)
            .IsRequired(false); // Make Icon field optional for EF validation

        // Configure Testimonial ClientPhotoUrl field as optional for Entity Framework validation
        builder.Entity<Testimonial>()
            .Property(t => t.ClientPhotoUrl)
            .IsRequired(false); // Make ClientPhotoUrl field optional for EF validation

        // Configure Project ImageUrl field as optional for Entity Framework validation
        builder.Entity<Project>()
            .Property(p => p.ImageUrl)
            .IsRequired(false); // Make ImageUrl field optional for EF validation

        // Configure Client LogoUrl field as optional for Entity Framework validation
        builder.Entity<Client>()
            .Property(c => c.LogoUrl)
            .IsRequired(false); // Make LogoUrl field optional for EF validation

        // Configure Feedback relationships
        builder.Entity<Feedback>()
            .HasOne(f => f.Client)
            .WithMany()
            .HasForeignKey(f => f.ClientId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<Feedback>()
            .HasOne(f => f.Project)
            .WithMany()
            .HasForeignKey(f => f.ProjectId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure Feedback optional fields
        builder.Entity<Feedback>()
            .Property(f => f.Rating)
            .IsRequired(false);

        builder.Entity<Feedback>()
            .Property(f => f.AdminResponse)
            .IsRequired(false);

        builder.Entity<Feedback>()
            .Property(f => f.AdminName)
            .IsRequired(false);

        // Configure LegalPage relationships
        builder.Entity<LegalPageSection>()
            .HasOne(lps => lps.LegalPage)
            .WithMany(lp => lp.Sections)
            .HasForeignKey(lps => lps.LegalPageId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure LegalPage unique slug
        builder.Entity<LegalPage>()
            .HasIndex(lp => lp.Slug)
            .IsUnique();

        // Configure AboutPage relationships
        builder.Entity<AboutPageSection>()
            .HasOne(aps => aps.AboutPage)
            .WithMany(ap => ap.Sections)
            .HasForeignKey(aps => aps.AboutPageId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure HeroSection relationships
        builder.Entity<HeroSlide>()
            .HasOne(hs => hs.HeroSection)
            .WithMany(h => h.Slides)
            .HasForeignKey(hs => hs.HeroSectionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure Chatbot entity relationships
        builder.Entity<ChatbotResponse>()
            .HasOne(cr => cr.ChatbotIntent)
            .WithMany(ci => ci.Responses)
            .HasForeignKey(cr => cr.ChatbotIntentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ChatbotKeyword>()
            .HasOne(ck => ck.ChatbotIntent)
            .WithMany(ci => ci.Keywords)
            .HasForeignKey(ck => ck.ChatbotIntentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ChatbotQuickAction>()
            .HasOne(cqa => cqa.ChatbotResponse)
            .WithMany(cr => cr.QuickActions)
            .HasForeignKey(cqa => cqa.ChatbotResponseId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ChatbotQuickAction>()
            .HasOne(cqa => cqa.ChatbotIntent)
            .WithMany()
            .HasForeignKey(cqa => cqa.ChatbotIntentId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure AboutPage optional fields
        builder.Entity<AboutPage>()
            .Property(ap => ap.HeroImageUrl)
            .IsRequired(false);

        builder.Entity<AboutPage>()
            .Property(ap => ap.StoryImageUrl)
            .IsRequired(false);

        builder.Entity<AboutPageSection>()
            .Property(aps => aps.ImageUrl)
            .IsRequired(false);

        // ServiceOption relationships
        builder.Entity<ServiceOption>()
            .HasOne(so => so.Service)
            .WithMany(s => s.ServiceOptions)
            .HasForeignKey(so => so.ServID)
            .OnDelete(DeleteBehavior.Cascade);

        // ServiceOptionFeature relationships
        builder.Entity<ServiceOptionFeature>()
            .HasOne(sof => sof.ServiceOption)
            .WithMany(so => so.Features)
            .HasForeignKey(sof => sof.OptID)
            .OnDelete(DeleteBehavior.Cascade);

        // PayrollRecord relationships
        builder.Entity<PayrollRecord>()
            .HasOne(pr => pr.TeamMember)
            .WithMany(tm => tm.PayrollRecords)
            .HasForeignKey(pr => pr.TeamMemberID)
            .OnDelete(DeleteBehavior.Restrict);

        // ProjectTask relationships
        builder.Entity<ProjectTask>()
            .HasOne(t => t.Project)
            .WithMany(p => p.Tasks)
            .HasForeignKey(t => t.ProjNo)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<ProjectTask>()
            .HasOne(t => t.TeamMember)
            .WithMany(tm => tm.Tasks)
            .HasForeignKey(t => t.TeamMemberID)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
