using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Technoloway.Core.Entities;

namespace Technoloway.Infrastructure.Data;

public static class DbInitializer
{
    public static async Task InitializeAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

        try
        {
            var context = services.GetRequiredService<ApplicationDbContext>();
            var userManager = services.GetRequiredService<UserManager<IdentityUser>>();
            var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();

            await context.Database.MigrateAsync();

            // Seed roles
            await SeedRolesAsync(roleManager);

            // Seed admin user
            await SeedAdminUserAsync(userManager);

            // Seed initial data
            await SeedInitialDataAsync(context);

            // Seed demo client users
            await SeedDemoClientUsersAsync(userManager, context);

            // Seed legal pages
            await LegalPageSeeder.SeedAsync(context);

            // Seed about page
            await AboutPageSeeder.SeedAsync(context);

            // Seed hero sections
            await HeroSectionSeeder.SeedAsync(context);

            // Seed chatbot data
            await ChatbotSeeder.SeedChatbotData(context);

            logger.LogInformation("Database initialized successfully.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while initializing the database.");
            throw;
        }
    }

    private static async Task SeedRolesAsync(RoleManager<IdentityRole> roleManager)
    {
        string[] roleNames = { "SuperAdmin", "ContentManager", "HRManager", "Client" };

        foreach (var roleName in roleNames)
        {
            if (!await roleManager.RoleExistsAsync(roleName))
            {
                await roleManager.CreateAsync(new IdentityRole(roleName));
            }
        }
    }

    private static async Task SeedAdminUserAsync(UserManager<IdentityUser> userManager)
    {
        const string adminEmail = "<EMAIL>";
        const string adminPassword = "Admin@123456";

        if (await userManager.FindByEmailAsync(adminEmail) == null)
        {
            var user = new IdentityUser
            {
                UserName = adminEmail,
                Email = adminEmail,
                EmailConfirmed = true
            };

            var result = await userManager.CreateAsync(user, adminPassword);

            if (result.Succeeded)
            {
                await userManager.AddToRoleAsync(user, "SuperAdmin");
            }
        }
    }

    private static async Task SeedDemoClientUsersAsync(UserManager<IdentityUser> userManager, ApplicationDbContext context)
    {
        // Demo client credentials
        var demoClients = new[]
        {
            new { Email = "<EMAIL>", Password = "Client@123456", ClientEmail = "<EMAIL>" },
            new { Email = "<EMAIL>", Password = "Client@123456", ClientEmail = "<EMAIL>" },
            new { Email = "<EMAIL>", Password = "Client@123456", ClientEmail = "<EMAIL>" }
        };

        foreach (var demoClient in demoClients)
        {
            if (await userManager.FindByEmailAsync(demoClient.Email) == null)
            {
                var user = new IdentityUser
                {
                    UserName = demoClient.Email,
                    Email = demoClient.Email,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(user, demoClient.Password);

                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(user, "Client");

                    // Link user to existing client record
                    var client = await context.Clients.FirstOrDefaultAsync(c => c.ContactEmail == demoClient.ClientEmail);
                    if (client != null)
                    {
                        client.UserId = user.Id;
                        context.Clients.Update(client);
                        await context.SaveChangesAsync();
                    }
                }
            }
        }
    }

    private static async Task SeedInitialDataAsync(ApplicationDbContext context)
    {
        // Seed Services
        if (!context.Services.Any())
        {
            var services = new List<Service>
            {
                new Service
                {
                    Name = "Web Development",
                    Description = "We create modern, responsive websites and web applications using the latest technologies.",
                    IconClass = "fas fa-laptop-code",
                    Price = 5000,
                    DisplayOrder = 1
                },
                new Service
                {
                    Name = "Mobile App Development",
                    Description = "We build native and cross-platform mobile applications for iOS and Android.",
                    IconClass = "fas fa-mobile-alt",
                    Price = 7500,
                    DisplayOrder = 2
                },
                new Service
                {
                    Name = "UI/UX Design",
                    Description = "We design intuitive and engaging user interfaces and experiences.",
                    IconClass = "fas fa-paint-brush",
                    Price = 3500,
                    DisplayOrder = 3
                },
                new Service
                {
                    Name = "Cloud Solutions",
                    Description = "We provide scalable cloud infrastructure and deployment solutions.",
                    IconClass = "fas fa-cloud",
                    Price = 4500,
                    DisplayOrder = 4
                }
            };

            await context.Services.AddRangeAsync(services);
        }

        // Seed Technologies
        if (!context.Technologies.Any())
        {
            var technologies = new List<Technology>
            {
                new Technology
                {
                    Name = "ASP.NET Core",
                    Description = "A cross-platform, high-performance framework for building modern, cloud-based, Internet-connected applications.",
                    IconUrl = "/images/technologies/dotnet.png",
                    DisplayOrder = 1
                },
                new Technology
                {
                    Name = "React",
                    Description = "A JavaScript library for building user interfaces, particularly single-page applications.",
                    IconUrl = "/images/technologies/react.png",
                    DisplayOrder = 2
                },
                new Technology
                {
                    Name = "Angular",
                    Description = "A platform and framework for building single-page client applications using HTML and TypeScript.",
                    IconUrl = "/images/technologies/angular.png",
                    DisplayOrder = 3
                },
                new Technology
                {
                    Name = "Node.js",
                    Description = "A JavaScript runtime built on Chrome's V8 JavaScript engine for building scalable network applications.",
                    IconUrl = "/images/technologies/nodejs.png",
                    DisplayOrder = 4
                },
                new Technology
                {
                    Name = "Azure",
                    Description = "Microsoft's cloud computing service for building, testing, deploying, and managing applications and services.",
                    IconUrl = "/images/technologies/azure.png",
                    DisplayOrder = 5
                }
            };

            await context.Technologies.AddRangeAsync(technologies);
        }

        // Seed Team Members
        if (!context.TeamMembers.Any())
        {
            var teamMembers = new List<TeamMember>
            {
                new TeamMember
                {
                    Name = "John Doe",
                    Position = "CEO & Founder",
                    Bio = "John has over 15 years of experience in software development and business leadership.",
                    PhotoUrl = "/images/team/john-doe.jpg",
                    Email = "<EMAIL>",
                    LinkedInUrl = "https://linkedin.com/in/johndoe",
                    TwitterUrl = "https://twitter.com/johndoe",
                    GithubUrl = "https://github.com/johndoe",
                    DisplayOrder = 1
                },
                new TeamMember
                {
                    Name = "Jane Smith",
                    Position = "CTO",
                    Bio = "Jane is an expert in cloud architecture and distributed systems.",
                    PhotoUrl = "/images/team/jane-smith.jpg",
                    Email = "<EMAIL>",
                    LinkedInUrl = "https://linkedin.com/in/janesmith",
                    TwitterUrl = "https://twitter.com/janesmith",
                    GithubUrl = "https://github.com/janesmith",
                    DisplayOrder = 2
                }
            };

            await context.TeamMembers.AddRangeAsync(teamMembers);
        }

        // Seed Testimonials
        if (!context.Testimonials.Any())
        {
            var testimonials = new List<Testimonial>
            {
                new Testimonial
                {
                    ClientName = "Michael Johnson",
                    ClientTitle = "CEO",
                    ClientCompany = "Acme Inc.",
                    ClientPhotoUrl = "/images/testimonials/michael-johnson.jpg",
                    Content = "Technoloway delivered our project on time and exceeded our expectations. Their team was professional and responsive throughout the entire process.",
                    Rating = 5,
                    DisplayOrder = 1
                },
                new Testimonial
                {
                    ClientName = "Sarah Williams",
                    ClientTitle = "Marketing Director",
                    ClientCompany = "Global Solutions",
                    ClientPhotoUrl = "/images/testimonials/sarah-williams.jpg",
                    Content = "We've been working with Technoloway for over 2 years now, and they've consistently delivered high-quality solutions for our digital needs.",
                    Rating = 5,
                    DisplayOrder = 2
                }
            };

            await context.Testimonials.AddRangeAsync(testimonials);
        }

        // Seed Site Settings
        if (!context.SiteSettings.Any())
        {
            var siteSettings = new List<SiteSetting>
            {
                new SiteSetting { Key = "SiteName", Value = "Technoloway", Group = "General" },
                new SiteSetting { Key = "SiteTagline", Value = "Innovative Software Solutions", Group = "General" },
                new SiteSetting { Key = "CompanyAddress", Value = "123 Tech Street, Silicon Valley, CA 94043", Group = "Contact", Icon = "fas fa-map-marker-alt" },
                new SiteSetting { Key = "CompanyPhone", Value = "+****************", Group = "Contact", Icon = "fas fa-phone" },
                new SiteSetting { Key = "CompanyEmail", Value = "<EMAIL>", Group = "Contact", Icon = "fas fa-envelope" },
                new SiteSetting { Key = "WorkingHours", Value = "Monday - Friday: 9:00 AM - 6:00 PM", Group = "Contact", Icon = "fas fa-clock" },
                new SiteSetting { Key = "FacebookUrl", Value = "https://facebook.com/technoloway", Group = "Social" },
                new SiteSetting { Key = "TwitterUrl", Value = "https://twitter.com/technoloway", Group = "Social" },
                new SiteSetting { Key = "LinkedInUrl", Value = "https://linkedin.com/company/technoloway", Group = "Social" },
                new SiteSetting { Key = "InstagramUrl", Value = "https://instagram.com/technoloway", Group = "Social" },
                // QuickLinks
                new SiteSetting { Key = "HomeLink", Value = "/", Group = "QuickLinks", Icon = "fas fa-home", Description = "Home page link" },
                new SiteSetting { Key = "AboutLink", Value = "/About", Group = "QuickLinks", Icon = "fas fa-info-circle", Description = "About us page link" },
                new SiteSetting { Key = "ServicesLink", Value = "/Services", Group = "QuickLinks", Icon = "fas fa-cogs", Description = "Services page link" },
                new SiteSetting { Key = "ProjectsLink", Value = "/Projects", Group = "QuickLinks", Icon = "fas fa-project-diagram", Description = "Projects page link" },
                new SiteSetting { Key = "ContactLink", Value = "/Home/Contact", Group = "QuickLinks", Icon = "fas fa-envelope", Description = "Contact page link" },
                new SiteSetting { Key = "BlogLink", Value = "/Blog", Group = "QuickLinks", Icon = "fas fa-blog", Description = "Blog page link" },
                new SiteSetting { Key = "GoogleMapsApiKey", Value = "", Group = "Integration", IsPublic = false },
                new SiteSetting { Key = "GoogleMapsLatitude", Value = "37.4224764", Group = "Integration" },
                new SiteSetting { Key = "GoogleMapsLongitude", Value = "-122.0842499", Group = "Integration" },
                new SiteSetting { Key = "StripePublishableKey", Value = "", Group = "Payment", IsPublic = false },
                new SiteSetting { Key = "StripeSecretKey", Value = "", Group = "Payment", IsPublic = false }
            };

            await context.SiteSettings.AddRangeAsync(siteSettings);
        }
        else
        {
            // Add missing settings for existing databases
            await AddMissingSiteSettingsAsync(context);
        }

        // Seed Job Listings
        if (!context.JobListings.Any())
        {
            var jobListings = new List<JobListing>
            {
                new JobListing
                {
                    Title = "Senior .NET Developer",
                    Description = "We are looking for a Senior .NET Developer to join our team and help us build high-quality, scalable web applications for our clients.\n\nAs a Senior .NET Developer, you will be responsible for designing, developing, and maintaining web applications using ASP.NET Core, C#, and related technologies. You will work closely with our project managers, designers, and other developers to deliver exceptional software solutions.",
                    Requirements = "- 5+ years of experience in .NET development\n- Strong proficiency in C#, ASP.NET Core, and Entity Framework Core\n- Experience with front-end technologies (HTML, CSS, JavaScript, React or Angular)\n- Knowledge of SQL Server and database design\n- Familiarity with Azure or AWS cloud services\n- Excellent problem-solving and communication skills\n- Bachelor's degree in Computer Science or related field (or equivalent experience)",
                    Location = "Silicon Valley, CA",
                    EmploymentType = "Full-time",
                    SalaryMin = 120000,
                    SalaryMax = 150000,
                    SalaryCurrency = "USD",
                    IsRemote = true,
                    IsActive = true,
                    ExpiresAt = DateTime.UtcNow.AddMonths(1)
                },
                new JobListing
                {
                    Title = "UI/UX Designer",
                    Description = "We are seeking a talented UI/UX Designer to create amazing user experiences for our web and mobile applications.\n\nAs a UI/UX Designer at Technoloway, you will be responsible for designing intuitive and engaging user interfaces that meet our clients' business objectives while providing exceptional user experiences. You will collaborate with product managers, developers, and stakeholders throughout the design process.",
                    Requirements = "- 3+ years of experience in UI/UX design for web and mobile applications\n- Proficiency in design tools such as Figma, Adobe XD, or Sketch\n- Strong portfolio demonstrating your design process and solutions\n- Knowledge of user-centered design principles and methodologies\n- Experience with responsive design and accessibility standards\n- Excellent communication and presentation skills\n- Bachelor's degree in Design, HCI, or related field (or equivalent experience)",
                    Location = "Silicon Valley, CA",
                    EmploymentType = "Full-time",
                    SalaryMin = 90000,
                    SalaryMax = 120000,
                    SalaryCurrency = "USD",
                    IsRemote = true,
                    IsActive = true,
                    ExpiresAt = DateTime.UtcNow.AddMonths(1)
                },
                new JobListing
                {
                    Title = "DevOps Engineer",
                    Description = "We are looking for a DevOps Engineer to help us build and maintain our CI/CD pipelines and cloud infrastructure.\n\nAs a DevOps Engineer at Technoloway, you will be responsible for implementing and managing our continuous integration and deployment processes, automating infrastructure provisioning, and ensuring the reliability and scalability of our applications in cloud environments.",
                    Requirements = "- 3+ years of experience in DevOps or similar role\n- Strong knowledge of CI/CD tools (Azure DevOps, Jenkins, GitHub Actions)\n- Experience with infrastructure as code (Terraform, ARM templates)\n- Proficiency in scripting languages (PowerShell, Bash, Python)\n- Experience with containerization technologies (Docker, Kubernetes)\n- Knowledge of cloud platforms (Azure, AWS)\n- Understanding of networking and security principles\n- Bachelor's degree in Computer Science or related field (or equivalent experience)",
                    Location = "Silicon Valley, CA",
                    EmploymentType = "Full-time",
                    SalaryMin = 110000,
                    SalaryMax = 140000,
                    SalaryCurrency = "USD",
                    IsRemote = true,
                    IsActive = true,
                    ExpiresAt = DateTime.UtcNow.AddMonths(1)
                }
            };

            await context.JobListings.AddRangeAsync(jobListings);
        }

        // Seed Clients first (required for Orders)
        if (!context.Clients.Any())
        {
            var clients = new List<Client>
            {
                new Client
                {
                    CompanyName = "TechCorp Solutions",
                    ContactName = "John Smith",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "+****************",
                    Address = "123 Business Ave",
                    City = "San Francisco",
                    State = "CA",
                    ZipCode = "94105",
                    Country = "United States"
                },
                new Client
                {
                    CompanyName = "Digital Innovations Inc",
                    ContactName = "Sarah Johnson",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "+****************",
                    Address = "456 Tech Street",
                    City = "Austin",
                    State = "TX",
                    ZipCode = "73301",
                    Country = "United States"
                },
                new Client
                {
                    CompanyName = "Global Enterprises",
                    ContactName = "Michael Brown",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "+****************",
                    Address = "789 Corporate Blvd",
                    City = "New York",
                    State = "NY",
                    ZipCode = "10001",
                    Country = "United States"
                }
            };

            await context.Clients.AddRangeAsync(clients);
            await context.SaveChangesAsync(); // Save clients first to get IDs
        }

        // Seed Orders (required for Projects)
        if (!context.Orders.Any())
        {
            var clients = await context.Clients.ToListAsync();
            if (clients.Any())
            {
                var orders = new List<Order>
                {
                    new Order
                    {
                        OrderID = 1,
                        OrderTitle = "E-Commerce Platform Development",
                        ClientID = clients[0].Id,
                        OrderManager = "John Smith",
                        OrderDesc = "Complete e-commerce platform with payment integration",
                        OrderDate = DateTime.UtcNow.AddMonths(-6),
                        OrderCost = 25000.00m,
                        OrderDiscountRate = 10,
                        OrderTotalDiscount = 2500.00m,
                        Status = "Completed",
                        Notes = "Successfully delivered on time"
                    },
                    new Order
                    {
                        OrderID = 2,
                        OrderTitle = "Mobile App Development",
                        ClientID = clients.Count > 1 ? clients[1].Id : clients[0].Id,
                        OrderManager = "Sarah Johnson",
                        OrderDesc = "Health and fitness tracking mobile application",
                        OrderDate = DateTime.UtcNow.AddMonths(-4),
                        OrderCost = 18000.00m,
                        OrderDiscountRate = 5,
                        OrderTotalDiscount = 900.00m,
                        Status = "Completed",
                        Notes = "Client very satisfied with the results"
                    },
                    new Order
                    {
                        OrderID = 3,
                        OrderTitle = "Corporate Website Redesign",
                        ClientID = clients.Count > 2 ? clients[2].Id : clients[0].Id,
                        OrderManager = "Michael Brown",
                        OrderDesc = "Complete website redesign with modern UI/UX",
                        OrderDate = DateTime.UtcNow.AddMonths(-2),
                        OrderCost = 12000.00m,
                        OrderDiscountRate = 0,
                        OrderTotalDiscount = 0.00m,
                        Status = "In Progress",
                        Notes = "Currently in development phase"
                    },
                    new Order
                    {
                        OrderID = 4,
                        OrderTitle = "ERP System Implementation",
                        ClientID = clients[0].Id,
                        OrderManager = "John Smith",
                        OrderDesc = "Enterprise resource planning system for manufacturing",
                        OrderDate = DateTime.UtcNow.AddMonths(-8),
                        OrderCost = 45000.00m,
                        OrderDiscountRate = 15,
                        OrderTotalDiscount = 6750.00m,
                        Status = "Completed",
                        Notes = "Large scale implementation completed successfully"
                    },
                    new Order
                    {
                        OrderID = 5,
                        OrderTitle = "Real Estate Platform",
                        ClientID = clients.Count > 1 ? clients[1].Id : clients[0].Id,
                        OrderManager = "Sarah Johnson",
                        OrderDesc = "Property listing platform with virtual tours",
                        OrderDate = DateTime.UtcNow.AddMonths(-5),
                        OrderCost = 22000.00m,
                        OrderDiscountRate = 8,
                        OrderTotalDiscount = 1760.00m,
                        Status = "Completed",
                        Notes = "Platform launched successfully"
                    },
                    new Order
                    {
                        OrderID = 6,
                        OrderTitle = "Restaurant Ordering System",
                        ClientID = clients.Count > 2 ? clients[2].Id : clients[0].Id,
                        OrderManager = "Michael Brown",
                        OrderDesc = "Mobile ordering system for restaurant chain",
                        OrderDate = DateTime.UtcNow.AddMonths(-7),
                        OrderCost = 15000.00m,
                        OrderDiscountRate = 5,
                        OrderTotalDiscount = 750.00m,
                        Status = "Completed",
                        Notes = "System deployed across all locations"
                    }
                };

                await context.Orders.AddRangeAsync(orders);
                await context.SaveChangesAsync();
            }
        }

        // Seed Projects
        if (!context.Projects.Any())
        {
            var projects = new List<Project>
            {
                new Project
                {
                    Name = "E-Commerce Platform",
                    Description = "A modern e-commerce platform with product management, shopping cart, payment processing, and order tracking.",
                    ClientName = "RetailTech Inc.",
                    ImageUrl = "/images/projects/ecommerce-platform.jpg",
                    ProjectUrl = "https://retailtech.example.com",
                    ProjCompletionDate = DateTime.UtcNow.AddMonths(-3),
                    IsFeatured = true,
                    DisplayOrder = 1,
                    OrderID = 1 // Web Development
                },
                new Project
                {
                    Name = "Health & Fitness App",
                    Description = "A comprehensive mobile app for tracking workouts, nutrition, and health metrics with personalized recommendations.",
                    ClientName = "FitLife Solutions",
                    ImageUrl = "/images/projects/fitness-app.jpg",
                    ProjectUrl = "https://fitlife.example.com",
                    ProjCompletionDate = DateTime.UtcNow.AddMonths(-5),
                    IsFeatured = true,
                    DisplayOrder = 2,
                    OrderID = 2 // Mobile App Development
                },
                new Project
                {
                    Name = "Financial Dashboard",
                    Description = "An intuitive financial dashboard for tracking investments, expenses, and financial goals with data visualization.",
                    ClientName = "FinTrack Systems",
                    ImageUrl = "/images/projects/financial-dashboard.jpg",
                    ProjectUrl = "https://fintrack.example.com",
                    ProjCompletionDate = DateTime.UtcNow.AddMonths(-2),
                    IsFeatured = true,
                    DisplayOrder = 3,
                    OrderID = 3 // UI/UX Design
                },
                new Project
                {
                    Name = "Enterprise Resource Planning System",
                    Description = "A cloud-based ERP system for managing inventory, HR, accounting, and customer relationships.",
                    ClientName = "Global Manufacturing Co.",
                    ImageUrl = "/images/projects/erp-system.jpg",
                    ProjectUrl = "https://globalmanufacturing.example.com",
                    ProjCompletionDate = DateTime.UtcNow.AddMonths(-7),
                    IsFeatured = false,
                    DisplayOrder = 4,
                    OrderID = 4 // Cloud Solutions
                },
                new Project
                {
                    Name = "Real Estate Listing Platform",
                    Description = "A web platform for property listings with advanced search, virtual tours, and agent management.",
                    ClientName = "Prime Properties",
                    ImageUrl = "/images/projects/real-estate-platform.jpg",
                    ProjectUrl = "https://primeproperties.example.com",
                    ProjCompletionDate = DateTime.UtcNow.AddMonths(-4),
                    IsFeatured = false,
                    DisplayOrder = 5,
                    OrderID = 5 // Web Development
                },
                new Project
                {
                    Name = "Restaurant Ordering System",
                    Description = "A mobile app for restaurant ordering with menu management, online ordering, and delivery tracking.",
                    ClientName = "Dining Innovations",
                    ImageUrl = "/images/projects/restaurant-app.jpg",
                    ProjectUrl = "https://dininginnovations.example.com",
                    ProjCompletionDate = DateTime.UtcNow.AddMonths(-6),
                    IsFeatured = false,
                    DisplayOrder = 6,
                    OrderID = 6 // Mobile App Development
                }
            };

            await context.Projects.AddRangeAsync(projects);
        }



        // Seed Invoices
        if (!context.Invoices.Any())
        {
            var clients = await context.Clients.ToListAsync();
            var projects = await context.Projects.ToListAsync();

            if (clients.Any())
            {
                var invoices = new List<Invoice>
                {
                    new Invoice
                    {
                        InvoiceNumber = "INV-2025-001",
                        IssueDate = DateTime.UtcNow.AddDays(-30),
                        DueDate = DateTime.UtcNow.AddDays(-15),
                        Amount = 5000.00m,
                        TaxRate = 8.5m,
                        TaxAmount = 425.00m,
                        TotalAmount = 5425.00m,
                        Status = "Paid",
                        Notes = "Website development project completed successfully.",
                        ClientId = clients[0].Id,
                        ContID = 1,
                        OrderID = 1
                    },
                    new Invoice
                    {
                        InvoiceNumber = "INV-2025-002",
                        IssueDate = DateTime.UtcNow.AddDays(-15),
                        DueDate = DateTime.UtcNow.AddDays(15),
                        Amount = 7500.00m,
                        TaxRate = 8.5m,
                        TaxAmount = 637.50m,
                        TotalAmount = 8137.50m,
                        Status = "Pending",
                        Notes = "Mobile app development - Phase 1",
                        ClientId = clients.Count > 1 ? clients[1].Id : clients[0].Id,
                        ContID = 2,
                        OrderID = 2
                    },
                    new Invoice
                    {
                        InvoiceNumber = "INV-2025-003",
                        IssueDate = DateTime.UtcNow.AddDays(-7),
                        DueDate = DateTime.UtcNow.AddDays(23),
                        Amount = 3200.00m,
                        TaxRate = 8.5m,
                        TaxAmount = 272.00m,
                        TotalAmount = 3472.00m,
                        Status = "Pending",
                        Notes = "Consulting services for Q1 2025",
                        ClientId = clients.Count > 2 ? clients[2].Id : clients[0].Id,
                        ContID = 3,
                        OrderID = 3
                    }
                };

                await context.Invoices.AddRangeAsync(invoices);
                await context.SaveChangesAsync(); // Save invoices to get IDs

                // Add sample invoice items
                var savedInvoices = await context.Invoices.ToListAsync();
                var invoiceItems = new List<InvoiceItem>();

                if (savedInvoices.Any())
                {
                    // Items for first invoice
                    invoiceItems.AddRange(new[]
                    {
                        new InvoiceItem
                        {
                            InvoiceId = savedInvoices[0].Id,
                            Description = "Website Design and Development",
                            Quantity = 1,
                            UnitPrice = 3000.00m,
                            TotalPrice = 3000.00m
                        },
                        new InvoiceItem
                        {
                            InvoiceId = savedInvoices[0].Id,
                            Description = "Content Management System Setup",
                            Quantity = 1,
                            UnitPrice = 2000.00m,
                            TotalPrice = 2000.00m
                        }
                    });

                    // Items for second invoice
                    if (savedInvoices.Count > 1)
                    {
                        invoiceItems.AddRange(new[]
                        {
                            new InvoiceItem
                            {
                                InvoiceId = savedInvoices[1].Id,
                                Description = "Mobile App Development - iOS",
                                Quantity = 1,
                                UnitPrice = 4000.00m,
                                TotalPrice = 4000.00m
                            },
                            new InvoiceItem
                            {
                                InvoiceId = savedInvoices[1].Id,
                                Description = "Mobile App Development - Android",
                                Quantity = 1,
                                UnitPrice = 3500.00m,
                                TotalPrice = 3500.00m
                            }
                        });
                    }

                    // Items for third invoice
                    if (savedInvoices.Count > 2)
                    {
                        invoiceItems.Add(new InvoiceItem
                        {
                            InvoiceId = savedInvoices[2].Id,
                            Description = "Technical Consulting Services",
                            Quantity = 40,
                            UnitPrice = 80.00m,
                            TotalPrice = 3200.00m
                        });
                    }

                    await context.InvoiceItems.AddRangeAsync(invoiceItems);
                }

                // Add sample payments
                var payments = new List<Payment>
                {
                    new Payment
                    {
                        InvoiceId = savedInvoices[0].Id,
                        TransactionId = "TXN-001-2025",
                        Amount = 5425.00m,
                        PaymentDate = DateTime.UtcNow.AddDays(-10),
                        PaymentMethod = "Bank Transfer",
                        Status = "Completed",
                        Notes = "Full payment received"
                    }
                };

                await context.Payments.AddRangeAsync(payments);
            }
        }

        // Seed Blog Posts
        if (!context.BlogPosts.Any())
        {
            var blogPosts = new List<BlogPost>
            {
                new BlogPost
                {
                    Title = "The Future of Web Development: Trends to Watch in 2025",
                    Slug = "future-web-development-trends-2025",
                    Content = "<p>The web development landscape is constantly evolving, with new technologies and methodologies emerging at a rapid pace. As we look ahead to 2025, several key trends are shaping the future of web development.</p><h3>1. WebAssembly Goes Mainstream</h3><p>WebAssembly (Wasm) has been gaining traction for years, but 2025 is poised to be the year it truly goes mainstream. With near-native performance and the ability to use languages beyond JavaScript, WebAssembly is enabling more complex applications on the web than ever before.</p><h3>2. AI-Driven Development</h3><p>Artificial intelligence is revolutionizing how we build websites and applications. From AI-assisted coding to automated testing and optimization, developers are leveraging machine learning to streamline workflows and create more intelligent applications.</p><h3>3. The Rise of Edge Computing</h3><p>As applications become more distributed, edge computing is becoming increasingly important. By processing data closer to where it's needed, edge computing reduces latency and improves performance, making it ideal for real-time applications.</p><h3>4. Serverless Architecture Evolution</h3><p>Serverless architecture continues to evolve, offering developers more flexibility and scalability. The focus is shifting from individual functions to complete serverless applications, with improved tooling and integration capabilities.</p><h3>5. Web Components and Micro-Frontends</h3><p>Component-based architecture is extending beyond individual frameworks, with web components and micro-frontends enabling more modular and maintainable applications across different technologies.</p><p>As we navigate these trends, the key for developers will be adaptability and continuous learning. The most successful web developers in 2025 will be those who can leverage these emerging technologies while maintaining a focus on performance, accessibility, and user experience.</p>",
                    Excerpt = "Explore the cutting-edge trends that will shape web development in 2025, from WebAssembly and AI-driven development to edge computing and serverless architecture evolution.",
                    FeaturedImageUrl = "/images/blog/web-development-trends.jpg",
                    IsPublished = true,
                    PublishedAt = DateTime.UtcNow.AddDays(-5),
                    Categories = "Web Development,Technology Trends"
                },
                new BlogPost
                {
                    Title = "Building Scalable Microservices with .NET Core",
                    Slug = "building-scalable-microservices-dotnet-core",
                    Content = "<p>Microservices architecture has become the standard for building large, complex applications that need to scale efficiently. In this article, we'll explore how to build scalable microservices using .NET Core.</p><h3>Understanding Microservices Architecture</h3><p>Microservices architecture breaks down applications into small, independent services that communicate through APIs. Each service is responsible for a specific business capability and can be developed, deployed, and scaled independently.</p><h3>Why .NET Core for Microservices?</h3><p>.NET Core offers several advantages for microservices development:</p><ul><li>Cross-platform compatibility</li><li>High performance</li><li>Built-in dependency injection</li><li>Excellent Docker support</li><li>Robust HTTP client and server libraries</li></ul><h3>Key Components for Scalable Microservices</h3><h4>1. API Gateways</h4><p>An API gateway serves as the entry point for clients, routing requests to appropriate services and handling cross-cutting concerns like authentication and rate limiting. In .NET Core, you can use tools like YARP or Ocelot to implement API gateways.</p><h4>2. Service Discovery</h4><p>As your microservices ecosystem grows, you need a way for services to find each other. Consul or the built-in service discovery in Kubernetes can be integrated with .NET Core applications.</p><h4>3. Resilience Patterns</h4><p>Implement resilience patterns like circuit breakers, retries, and timeouts using libraries such as Polly to ensure your microservices can handle failures gracefully.</p><h4>4. Containerization</h4><p>.NET Core applications can be easily containerized using Docker, making them portable and consistent across different environments.</p><h3>Communication Patterns</h3><p>Choose the right communication pattern for your microservices:</p><ul><li><strong>Synchronous communication</strong> using HTTP/gRPC for real-time interactions</li><li><strong>Asynchronous communication</strong> using message brokers like RabbitMQ or Azure Service Bus for decoupled operations</li></ul><h3>Monitoring and Observability</h3><p>Implement comprehensive monitoring using tools like Prometheus, Grafana, and OpenTelemetry to gain insights into your microservices' performance and health.</p><p>By following these principles and leveraging the capabilities of .NET Core, you can build microservices that scale efficiently and are resilient to failures.</p>",
                    Excerpt = "Learn how to build scalable, resilient microservices using .NET Core, including best practices for API gateways, service discovery, communication patterns, and monitoring.",
                    FeaturedImageUrl = "/images/blog/microservices-dotnet.jpg",
                    IsPublished = true,
                    PublishedAt = DateTime.UtcNow.AddDays(-12),
                    Categories = "Web Development,.NET,Microservices"
                },
                new BlogPost
                {
                    Title = "Optimizing Mobile App Performance: Best Practices",
                    Slug = "optimizing-mobile-app-performance-best-practices",
                    Content = "<p>Mobile app performance can make or break user experience. In this guide, we'll explore best practices for optimizing mobile app performance across both iOS and Android platforms.</p><h3>Why Performance Matters</h3><p>Studies show that users abandon apps that take more than 3 seconds to load, and a 1-second delay in response time can reduce conversion rates by up to 7%. Performance isn't just a technical concern—it directly impacts your business metrics.</p><h3>Performance Optimization Strategies</h3><h4>1. Efficient Network Requests</h4><p>Network operations are often the biggest performance bottleneck in mobile apps. Implement these strategies to optimize network performance:</p><ul><li>Minimize request size by compressing data</li><li>Implement efficient caching strategies</li><li>Use batch requests to reduce API calls</li><li>Implement request prioritization</li></ul><h4>2. Optimize Images and Media</h4><p>Images and media can significantly impact app size and loading times:</p><ul><li>Use appropriate image formats (WebP for Android, HEIC for iOS)</li><li>Implement lazy loading for images</li><li>Use responsive images based on screen size</li><li>Compress videos and audio files</li></ul><h4>3. Efficient UI Rendering</h4><p>A smooth UI is critical for user experience:</p><ul><li>Minimize view hierarchy depth</li><li>Use hardware acceleration when appropriate</li><li>Implement view recycling for lists</li><li>Avoid expensive operations on the main thread</li></ul><h4>4. Memory Management</h4><p>Poor memory management leads to crashes and sluggish performance:</p><ul><li>Implement proper resource cleanup</li><li>Avoid memory leaks by managing object lifecycles</li><li>Use weak references for callbacks</li><li>Monitor memory usage during development</li></ul><h3>Performance Testing and Monitoring</h3><p>Implement a comprehensive performance testing strategy:</p><ul><li>Use automated performance testing tools</li><li>Conduct real-device testing across different device tiers</li><li>Implement performance monitoring in production</li><li>Track key performance indicators (KPIs) like startup time, frame rate, and memory usage</li></ul><p>By following these best practices, you can significantly improve your mobile app's performance, leading to better user retention, engagement, and overall success.</p>",
                    Excerpt = "Discover essential strategies for optimizing mobile app performance, including network optimization, efficient UI rendering, memory management, and performance monitoring techniques.",
                    FeaturedImageUrl = "/images/blog/mobile-performance.jpg",
                    IsPublished = true,
                    PublishedAt = DateTime.UtcNow.AddDays(-20),
                    Categories = "Mobile Development,Performance Optimization"
                }
            };

            await context.BlogPosts.AddRangeAsync(blogPosts);
        }

        await context.SaveChangesAsync();
    }

    private static async Task AddMissingSiteSettingsAsync(ApplicationDbContext context)
    {
        var missingSetting = new List<SiteSetting>();

        // Check for WorkingHours setting
        if (!await context.SiteSettings.AnyAsync(s => s.Key == "WorkingHours"))
        {
            missingSetting.Add(new SiteSetting
            {
                Key = "WorkingHours",
                Value = "Monday - Friday: 9:00 AM - 6:00 PM",
                Group = "Contact",
                IsPublic = true,
                Description = "Company working hours displayed on contact page",
                Icon = "fas fa-clock",
                CreatedAt = DateTime.UtcNow
            });
        }

        // Ensure all contact settings have proper icons
        var contactSettingsToUpdate = await context.SiteSettings
            .Where(s => s.Group == "Contact" && (string.IsNullOrEmpty(s.Icon) || s.Icon == ""))
            .ToListAsync();

        foreach (var setting in contactSettingsToUpdate)
        {
            setting.Icon = setting.Key.ToLower() switch
            {
                "companyaddress" => "fas fa-map-marker-alt",
                "companyphone" => "fas fa-phone",
                "companyemail" => "fas fa-envelope",
                "workinghours" => "fas fa-clock",
                "companyfax" => "fas fa-fax",
                "companywebsite" => "fas fa-globe",
                "companymobile" => "fas fa-mobile-alt",
                "companywhatsapp" => "fab fa-whatsapp",
                "companytelegram" => "fab fa-telegram",
                "companyskype" => "fab fa-skype",
                _ => "fas fa-info-circle"
            };
            setting.UpdatedAt = DateTime.UtcNow;
        }

        if (contactSettingsToUpdate.Any())
        {
            Console.WriteLine($"Updated icons for {contactSettingsToUpdate.Count} contact settings.");
        }

        // Ensure all social settings have proper icons
        var socialSettingsToUpdate = await context.SiteSettings
            .Where(s => s.Group == "Social" && (string.IsNullOrEmpty(s.Icon) || s.Icon == ""))
            .ToListAsync();

        foreach (var setting in socialSettingsToUpdate)
        {
            setting.Icon = setting.Key.ToLower() switch
            {
                "facebookurl" => "fab fa-facebook-f",
                "twitterurl" => "fab fa-twitter",
                "linkedinurl" => "fab fa-linkedin-in",
                "instagramurl" => "fab fa-instagram",
                "youtubeurl" => "fab fa-youtube",
                "githuburl" => "fab fa-github",
                "whatsappurl" => "fab fa-whatsapp",
                "telegramurl" => "fab fa-telegram",
                "discordurl" => "fab fa-discord",
                "tiktokurl" => "fab fa-tiktok",
                "pinteresturl" => "fab fa-pinterest",
                "snapchaturl" => "fab fa-snapchat",
                _ => "fas fa-link"
            };
            setting.UpdatedAt = DateTime.UtcNow;
        }

        if (socialSettingsToUpdate.Any())
        {
            Console.WriteLine($"Updated icons for {socialSettingsToUpdate.Count} social settings.");
        }

        // Add missing QuickLinks settings
        var quickLinksToAdd = new List<SiteSetting>();

        if (!await context.SiteSettings.AnyAsync(s => s.Key == "HomeLink" && s.Group == "QuickLinks"))
        {
            quickLinksToAdd.Add(new SiteSetting { Key = "HomeLink", Value = "/", Group = "QuickLinks", Icon = "fas fa-home", Description = "Home page link", IsPublic = true, CreatedAt = DateTime.UtcNow });
        }

        if (!await context.SiteSettings.AnyAsync(s => s.Key == "AboutLink" && s.Group == "QuickLinks"))
        {
            quickLinksToAdd.Add(new SiteSetting { Key = "AboutLink", Value = "/About", Group = "QuickLinks", Icon = "fas fa-info-circle", Description = "About us page link", IsPublic = true, CreatedAt = DateTime.UtcNow });
        }

        if (!await context.SiteSettings.AnyAsync(s => s.Key == "ServicesLink" && s.Group == "QuickLinks"))
        {
            quickLinksToAdd.Add(new SiteSetting { Key = "ServicesLink", Value = "/Services", Group = "QuickLinks", Icon = "fas fa-cogs", Description = "Services page link", IsPublic = true, CreatedAt = DateTime.UtcNow });
        }

        if (!await context.SiteSettings.AnyAsync(s => s.Key == "ProjectsLink" && s.Group == "QuickLinks"))
        {
            quickLinksToAdd.Add(new SiteSetting { Key = "ProjectsLink", Value = "/Projects", Group = "QuickLinks", Icon = "fas fa-project-diagram", Description = "Projects page link", IsPublic = true, CreatedAt = DateTime.UtcNow });
        }

        if (!await context.SiteSettings.AnyAsync(s => s.Key == "ContactLink" && s.Group == "QuickLinks"))
        {
            quickLinksToAdd.Add(new SiteSetting { Key = "ContactLink", Value = "/Home/Contact", Group = "QuickLinks", Icon = "fas fa-envelope", Description = "Contact page link", IsPublic = true, CreatedAt = DateTime.UtcNow });
        }

        if (!await context.SiteSettings.AnyAsync(s => s.Key == "BlogLink" && s.Group == "QuickLinks"))
        {
            quickLinksToAdd.Add(new SiteSetting { Key = "BlogLink", Value = "/Blog", Group = "QuickLinks", Icon = "fas fa-blog", Description = "Blog page link", IsPublic = true, CreatedAt = DateTime.UtcNow });
        }

        if (quickLinksToAdd.Any())
        {
            missingSetting.AddRange(quickLinksToAdd);
            Console.WriteLine($"Added {quickLinksToAdd.Count} missing QuickLinks settings.");
        }

        // Fix existing "Working Hours" key to "WorkingHours" (without space)
        var existingWorkingHours = await context.SiteSettings.FirstOrDefaultAsync(s => s.Key == "Working Hours");
        if (existingWorkingHours != null)
        {
            existingWorkingHours.Key = "WorkingHours";
            existingWorkingHours.UpdatedAt = DateTime.UtcNow;
            context.SiteSettings.Update(existingWorkingHours);
        }

        // Add other missing settings as needed in the future
        // Example:
        // if (!await context.SiteSettings.AnyAsync(s => s.Key == "SomeNewSetting"))
        // {
        //     missingSetting.Add(new SiteSetting { ... });
        // }

        if (missingSetting.Any())
        {
            await context.SiteSettings.AddRangeAsync(missingSetting);
            await context.SaveChangesAsync();
            Console.WriteLine($"Added {missingSetting.Count} missing site settings to database.");
        }
    }
}
