using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Services;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
public class ServicesController : Controller
{
    private readonly IRepository<Service> _serviceRepository;
    private readonly IRepository<Category> _categoryRepository;
    private readonly IRepository<ServiceOption> _serviceOptionRepository;
    private readonly IRepository<ServiceOptionFeature> _serviceOptionFeatureRepository;
    private readonly IFileUploadService _fileUploadService;

    public ServicesController(
        IRepository<Service> serviceRepository,
        IRepository<Category> categoryRepository,
        IRepository<ServiceOption> serviceOptionRepository,
        IRepository<ServiceOptionFeature> serviceOptionFeatureRepository,
        IFileUploadService fileUploadService)
    {
        _serviceRepository = serviceRepository;
        _categoryRepository = categoryRepository;
        _serviceOptionRepository = serviceOptionRepository;
        _serviceOptionFeatureRepository = serviceOptionFeatureRepository;
        _fileUploadService = fileUploadService;
    }

    public async Task<IActionResult> Index()
    {
        var viewModel = new ServiceManagementViewModel
        {
            Categories = await _categoryRepository.GetAll()
                .Where(c => !c.IsDeleted)
                .Include(c => c.Parent)
                .Include(c => c.Children)
                .OrderBy(c => c.ParentID ?? 0)
                .ThenBy(c => c.CategName)
                .ToListAsync(),

            Services = await _serviceRepository.GetAll()
                .Where(s => !s.IsDeleted)
                .Include(s => s.Category)
                .Include(s => s.ServiceOptions)
                    .ThenInclude(so => so.Features)
                .OrderBy(s => s.DisplayOrder)
                .ToListAsync()
        };

        return View(viewModel);
    }

    // CATEGORY MANAGEMENT
    public async Task<IActionResult> CreateCategory()
    {
        var categories = await _categoryRepository.GetAll()
            .Where(c => !c.IsDeleted && c.ParentID == null)
            .ToListAsync();

        ViewBag.ParentCategories = new SelectList(categories, "Id", "CategName");

        var viewModel = new CategoryViewModel();
        return PartialView("_CreateCategoryModal", viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateCategory(CategoryViewModel viewModel)
    {
        try
        {
            if (ModelState.IsValid)
            {
                var category = viewModel.ToEntity();
                category.CreatedAt = DateTime.UtcNow;

                await _categoryRepository.AddAsync(category);

                return Json(new { success = true, message = "Category created successfully." });
            }
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error creating category: {ex.Message}" });
        }

        return Json(new { success = false, message = "Invalid data provided." });
    }

    public async Task<IActionResult> EditCategory(int id)
    {
        var category = await _categoryRepository.GetByIdAsync(id);
        if (category == null || category.IsDeleted)
        {
            return NotFound();
        }

        var categories = await _categoryRepository.GetAll()
            .Where(c => !c.IsDeleted && c.ParentID == null && c.Id != id)
            .ToListAsync();

        ViewBag.ParentCategories = new SelectList(categories, "Id", "CategName", category.ParentID);

        var viewModel = CategoryViewModel.FromEntity(category);
        return PartialView("_EditCategoryModal", viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditCategory(CategoryViewModel viewModel)
    {
        try
        {
            if (ModelState.IsValid)
            {
                var category = await _categoryRepository.GetByIdAsync(viewModel.Id);
                if (category == null || category.IsDeleted)
                {
                    return Json(new { success = false, message = "Category not found." });
                }

                category.CategName = viewModel.CategName;
                category.CategDesc = viewModel.CategDesc;
                category.ParentID = viewModel.ParentID;
                category.UpdatedAt = DateTime.UtcNow;

                await _categoryRepository.UpdateAsync(category);

                return Json(new { success = true, message = "Category updated successfully." });
            }
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error updating category: {ex.Message}" });
        }

        return Json(new { success = false, message = "Invalid data provided." });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteCategory(int id)
    {
        try
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null || category.IsDeleted)
            {
                return Json(new { success = false, message = "Category not found." });
            }

            // Check if category has services
            var hasServices = await _serviceRepository.GetAll()
                .AnyAsync(s => s.CategID == id && !s.IsDeleted);

            if (hasServices)
            {
                return Json(new { success = false, message = "Cannot delete category that has services. Please move or delete the services first." });
            }

            // Check if category has children
            var hasChildren = await _categoryRepository.GetAll()
                .AnyAsync(c => c.ParentID == id && !c.IsDeleted);

            if (hasChildren)
            {
                return Json(new { success = false, message = "Cannot delete category that has subcategories. Please move or delete the subcategories first." });
            }

            category.IsDeleted = true;
            category.UpdatedAt = DateTime.UtcNow;

            await _categoryRepository.UpdateAsync(category);

            return Json(new { success = true, message = "Category deleted successfully." });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error deleting category: {ex.Message}" });
        }
    }

    // SERVICE MANAGEMENT
    public async Task<IActionResult> Create(int? categoryId)
    {
        var categories = await _categoryRepository.GetAll()
            .Where(c => !c.IsDeleted)
            .ToListAsync();

        ViewBag.Categories = categories.Select(c => new SelectListItem
        {
            Value = c.Id.ToString(),
            Text = c.ParentID == null ? c.CategName : $"-- {c.CategName}",
            Selected = categoryId.HasValue && c.Id == categoryId.Value
        }).ToList();

        var viewModel = new ServiceViewModel
        {
            IsActive = true,
            DisplayOrder = 0
        };

        if (categoryId.HasValue)
        {
            viewModel.CategID = categoryId.Value;
        }

        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(ServiceViewModel viewModel)
    {
        try
        {
            // Handle file upload if provided
            if (viewModel.IconFile != null && viewModel.IconFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.IconFile))
                {
                    ModelState.AddModelError("IconFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    viewModel.IconClass = await _fileUploadService.UploadImageAsync(viewModel.IconFile, "services");
                }
            }

            if (ModelState.IsValid)
            {
                var service = viewModel.ToEntity();
                service.CreatedAt = DateTime.UtcNow;
                service.UpdatedAt = DateTime.UtcNow;
                service.IsActive = true; // Ensure new services are active by default

                await _serviceRepository.AddAsync(service);
                TempData["SuccessMessage"] = "Service created successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error creating service: {ex.Message}");
        }

        // Reload categories for dropdown
        var categories = await _categoryRepository.GetAll()
            .Where(c => !c.IsDeleted)
            .ToListAsync();
        ViewBag.Categories = new SelectList(categories, "Id", "CategName", viewModel.CategID);

        return View(viewModel);
    }

    // SERVICE OPTION MANAGEMENT
    public async Task<IActionResult> CreateServiceOption(int serviceId)
    {
        var service = await _serviceRepository.GetByIdAsync(serviceId);
        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = new ServiceOptionViewModel
        {
            ServID = serviceId,
            OptAvailability = "Available"
        };

        ViewBag.ServiceName = service.Name;
        return PartialView("_CreateServiceOptionModal", viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateServiceOption(ServiceOptionViewModel viewModel)
    {
        try
        {
            if (ModelState.IsValid)
            {
                // Get next OptID for this service
                var maxOptId = await _serviceOptionRepository.GetAll()
                    .Where(so => so.ServID == viewModel.ServID)
                    .MaxAsync(so => (int?)so.OptID) ?? 0;

                var serviceOption = viewModel.ToEntity();
                serviceOption.OptID = maxOptId + 1;
                serviceOption.CreatedAt = DateTime.UtcNow;

                await _serviceOptionRepository.AddAsync(serviceOption);

                return Json(new { success = true, message = "Service option created successfully." });
            }
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error creating service option: {ex.Message}" });
        }

        return Json(new { success = false, message = "Invalid data provided." });
    }

    public async Task<IActionResult> EditServiceOption(int id)
    {
        var serviceOption = await _serviceOptionRepository.GetAll()
            .Include(so => so.Service)
            .FirstOrDefaultAsync(so => so.Id == id);

        if (serviceOption == null || serviceOption.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = ServiceOptionViewModel.FromEntity(serviceOption);
        ViewBag.ServiceName = serviceOption.Service.Name;

        return PartialView("_EditServiceOptionModal", viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditServiceOption(ServiceOptionViewModel viewModel)
    {
        try
        {
            if (ModelState.IsValid)
            {
                var serviceOption = await _serviceOptionRepository.GetByIdAsync(viewModel.Id);
                if (serviceOption == null || serviceOption.IsDeleted)
                {
                    return Json(new { success = false, message = "Service option not found." });
                }

                serviceOption.OptName = viewModel.OptName;
                serviceOption.OptCost = viewModel.OptCost;
                serviceOption.OptDiscountRate = viewModel.OptDiscountRate;
                serviceOption.OptTotalDiscount = viewModel.OptTotalDiscount;
                serviceOption.OptAvailability = viewModel.OptAvailability;
                serviceOption.OptDesc = viewModel.OptDesc;
                serviceOption.UpdatedAt = DateTime.UtcNow;

                await _serviceOptionRepository.UpdateAsync(serviceOption);

                return Json(new { success = true, message = "Service option updated successfully." });
            }
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error updating service option: {ex.Message}" });
        }

        return Json(new { success = false, message = "Invalid data provided." });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteServiceOption(int id)
    {
        try
        {
            var serviceOption = await _serviceOptionRepository.GetByIdAsync(id);
            if (serviceOption == null || serviceOption.IsDeleted)
            {
                return Json(new { success = false, message = "Service option not found." });
            }

            // Check if option has features
            var hasFeatures = await _serviceOptionFeatureRepository.GetAll()
                .AnyAsync(f => f.OptID == serviceOption.Id && !f.IsDeleted);

            if (hasFeatures)
            {
                return Json(new { success = false, message = "Cannot delete service option that has features. Please delete the features first." });
            }

            serviceOption.IsDeleted = true;
            serviceOption.UpdatedAt = DateTime.UtcNow;

            await _serviceOptionRepository.UpdateAsync(serviceOption);

            return Json(new { success = true, message = "Service option deleted successfully." });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error deleting service option: {ex.Message}" });
        }
    }

    // SERVICE OPTION FEATURE MANAGEMENT
    public async Task<IActionResult> CreateServiceOptionFeature(int serviceOptionId)
    {
        var serviceOption = await _serviceOptionRepository.GetAll()
            .Include(so => so.Service)
            .FirstOrDefaultAsync(so => so.Id == serviceOptionId);

        if (serviceOption == null || serviceOption.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = new ServiceOptionFeatureViewModel
        {
            OptID = serviceOption.Id, // Use the primary key, not OptID
            ServiceOptionId = serviceOptionId,
            FeatAvailability = "Included"
        };

        ViewBag.ServiceName = serviceOption.Service.Name;
        ViewBag.OptionName = serviceOption.OptName;
        return PartialView("_CreateServiceOptionFeatureModal", viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateServiceOptionFeature(ServiceOptionFeatureViewModel viewModel)
    {
        try
        {
            if (ModelState.IsValid)
            {
                // Get next FeatID
                var maxFeatId = await _serviceOptionFeatureRepository.GetAll()
                    .MaxAsync(f => (int?)f.FeatID) ?? 0;

                var feature = viewModel.ToEntity();
                feature.FeatID = maxFeatId + 1;
                feature.CreatedAt = DateTime.UtcNow;

                await _serviceOptionFeatureRepository.AddAsync(feature);

                return Json(new { success = true, message = "Service option feature created successfully." });
            }
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error creating service option feature: {ex.Message}" });
        }

        return Json(new { success = false, message = "Invalid data provided." });
    }

    public async Task<IActionResult> EditServiceOptionFeature(int id)
    {
        var feature = await _serviceOptionFeatureRepository.GetAll()
            .Include(f => f.ServiceOption)
                .ThenInclude(so => so.Service)
            .FirstOrDefaultAsync(f => f.Id == id);

        if (feature == null || feature.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = ServiceOptionFeatureViewModel.FromEntity(feature);
        ViewBag.ServiceName = feature.ServiceOption.Service.Name;
        ViewBag.OptionName = feature.ServiceOption.OptName;

        return PartialView("_EditServiceOptionFeatureModal", viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditServiceOptionFeature(ServiceOptionFeatureViewModel viewModel)
    {
        try
        {
            if (ModelState.IsValid)
            {
                var feature = await _serviceOptionFeatureRepository.GetByIdAsync(viewModel.Id);
                if (feature == null || feature.IsDeleted)
                {
                    return Json(new { success = false, message = "Service option feature not found." });
                }

                feature.FeatName = viewModel.FeatName;
                feature.FeatCost = viewModel.FeatCost;
                feature.FeatDiscountRate = viewModel.FeatDiscountRate;
                feature.FeatTotalDiscount = viewModel.FeatTotalDiscount;
                feature.FeatAvailability = viewModel.FeatAvailability;
                feature.FeatDesc = viewModel.FeatDesc;
                feature.UpdatedAt = DateTime.UtcNow;

                await _serviceOptionFeatureRepository.UpdateAsync(feature);

                return Json(new { success = true, message = "Service option feature updated successfully." });
            }
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error updating service option feature: {ex.Message}" });
        }

        return Json(new { success = false, message = "Invalid data provided." });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteServiceOptionFeature(int id)
    {
        try
        {
            var feature = await _serviceOptionFeatureRepository.GetByIdAsync(id);
            if (feature == null || feature.IsDeleted)
            {
                return Json(new { success = false, message = "Service option feature not found." });
            }

            feature.IsDeleted = true;
            feature.UpdatedAt = DateTime.UtcNow;

            await _serviceOptionFeatureRepository.UpdateAsync(feature);

            return Json(new { success = true, message = "Service option feature deleted successfully." });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error deleting service option feature: {ex.Message}" });
        }
    }

    public async Task<IActionResult> Details(int id)
    {
        var service = await _serviceRepository.GetAll()
            .Include(s => s.Category)
            .Include(s => s.ServiceOptions)
                .ThenInclude(so => so.Features)
            .FirstOrDefaultAsync(s => s.Id == id);

        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        return View(service);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = ServiceViewModel.FromEntity(service);
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, ServiceViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            var existingService = await _serviceRepository.GetByIdAsync(id);
            if (existingService == null || existingService.IsDeleted)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.IconFile != null && viewModel.IconFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.IconFile))
                {
                    ModelState.AddModelError("IconFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingService.IconClass) && existingService.IconClass.StartsWith("/images/"))
                    {
                        oldFileName = Path.GetFileName(existingService.IconClass);
                    }

                    viewModel.IconClass = await _fileUploadService.UploadImageAsync(viewModel.IconFile, "services", oldFileName);
                }
            }
            else
            {
                // Keep existing icon if no new file uploaded
                viewModel.IconClass = existingService.IconClass;
            }

            if (ModelState.IsValid)
            {
                viewModel.UpdateEntity(existingService);
                existingService.UpdatedAt = DateTime.UtcNow;

                await _serviceRepository.UpdateAsync(existingService);
                TempData["SuccessMessage"] = "Service updated successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await ServiceExists(viewModel.Id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error updating service: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        return View(service);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        if (service == null || service.IsDeleted)
        {
            return NotFound();
        }

        service.IsDeleted = true;
        service.UpdatedAt = DateTime.UtcNow;
        await _serviceRepository.UpdateAsync(service);

        return RedirectToAction(nameof(Index));
    }

    private async Task<bool> ServiceExists(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        return service != null && !service.IsDeleted;
    }
}
