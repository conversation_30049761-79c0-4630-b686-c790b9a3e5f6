@model Technoloway.Web.Areas.Admin.Models.ServiceManagementViewModel

@{
    ViewData["Title"] = "Service Management";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Service Management</h1>
            <p class="text-muted mb-0">Manage categories, services, options, and features</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary" onclick="showCategoryModal()">
                <i class="fas fa-plus"></i>
                Add Category
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add Service
            </a>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="serviceManagementTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab">
                <i class="fas fa-folder me-2"></i>Categories
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="services-tab" data-bs-toggle="tab" data-bs-target="#services" type="button" role="tab">
                <i class="fas fa-server me-2"></i>Services
            </button>
        </li>
    </ul>

    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Categories</p>
                        <h3 class="admin-stat-number">@Model.Categories.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Services</p>
                        <h3 class="admin-stat-number">@Model.Services.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Service Options</p>
                        <h3 class="admin-stat-number">@Model.Services.Sum(s => s.ServiceOptions.Count)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Features</p>
                        <h3 class="admin-stat-number">@Model.Services.Sum(s => s.ServiceOptions.Sum(so => so.Features.Count))</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="serviceManagementTabContent">
        <!-- Categories Tab -->
        <div class="tab-pane fade show active" id="categories" role="tabpanel">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
                    <div>
                        <h5 class="mb-0 fw-bold text-gray-800">Categories</h5>
                        <p class="text-muted mb-0 small">@Model.Categories.Count() total categories</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-sm" onclick="showCategoryModal()">
                            <i class="fas fa-plus me-1"></i>
                            Add Category
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if (Model.Categories.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="categoriesTable">
                                <thead>
                                    <tr>
                                        <th class="border-0 fw-semibold">Category</th>
                                        <th class="border-0 fw-semibold">Parent</th>
                                        <th class="border-0 fw-semibold">Services Count</th>
                                        <th class="border-0 fw-semibold">Created</th>
                                        <th class="border-0 fw-semibold">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var category in Model.Categories.OrderBy(c => c.ParentID ?? 0).ThenBy(c => c.CategName))
                                    {
                                        <tr>
                                            <td class="border-0">
                                                <div class="d-flex align-items-center">
                                                    @if (category.ParentID == null)
                                                    {
                                                        <i class="fas fa-folder text-primary me-2"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="fas fa-folder-open text-secondary me-2 ms-3"></i>
                                                    }
                                                    <div>
                                                        <div class="fw-semibold text-gray-800">@category.CategName</div>
                                                        @if (!string.IsNullOrEmpty(category.CategDesc))
                                                        {
                                                            <div class="text-muted small">@category.CategDesc</div>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="border-0">
                                                @if (category.Parent != null)
                                                {
                                                    <span class="badge bg-light text-dark">@category.Parent.CategName</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Root Category</span>
                                                }
                                            </td>
                                            <td class="border-0">
                                                <span class="badge bg-info">@category.Services.Count</span>
                                            </td>
                                            <td class="border-0">
                                                <span class="text-muted small">@category.CreatedAt.ToString("MMM dd, yyyy")</span>
                                            </td>
                                            <td class="border-0">
                                                <div class="d-flex gap-1">
                                                    <button class="btn btn-outline-primary btn-sm" onclick="editCategory(@category.Id)" title="Edit Category">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteCategory(@category.Id)" title="Delete Category">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-folder text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted">No categories found</h5>
                            <p class="text-muted">Create your first category to organize services.</p>
                            <button class="btn btn-primary" onclick="showCategoryModal()">
                                <i class="fas fa-plus me-2"></i>
                                Create First Category
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Services Tab -->
        <div class="tab-pane fade" id="services" role="tabpanel">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
                    <div>
                        <h5 class="mb-0 fw-bold text-gray-800">Services</h5>
                        <p class="text-muted mb-0 small">@Model.Services.Count() total services</p>
                    </div>
                    <div class="d-flex gap-2">
                        <a asp-action="Create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            Add Service
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if (Model.Services.Any())
                    {
                        <div class="accordion" id="servicesAccordion">
                            @foreach (var service in Model.Services.OrderBy(s => s.DisplayOrder))
                            {
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading@(service.Id)">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse@(service.Id)">
                                            <div class="d-flex align-items-center w-100">
                                                <div class="me-3">
                                                    @if (!string.IsNullOrEmpty(service.IconClass))
                                                    {
                                                        <i class="@service.IconClass"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="fas fa-cog"></i>
                                                    }
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="fw-semibold">@service.Name</div>
                                                    <div class="text-muted small">@service.Category.CategName • @service.Price.ToString("C")</div>
                                                </div>
                                                <div class="me-3">
                                                    @if (service.IsActive)
                                                    {
                                                        <span class="badge bg-success">Active</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    }
                                                </div>
                                                <div class="d-flex gap-1">
                                                    <button class="btn btn-outline-primary btn-sm" onclick="event.stopPropagation(); editService(@service.Id)" title="Edit Service">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success btn-sm" onclick="event.stopPropagation(); addServiceOption(@service.Id)" title="Add Option">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="collapse@(service.Id)" class="accordion-collapse collapse" data-bs-parent="#servicesAccordion">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="fw-bold mb-3">Service Details</h6>
                                                    <p class="text-muted">@service.Description</p>
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <small class="text-muted">Price:</small>
                                                            <div class="fw-semibold">@service.Price.ToString("C")</div>
                                                        </div>
                                                        <div class="col-6">
                                                            <small class="text-muted">Manager:</small>
                                                            <div class="fw-semibold">@(service.ServManager ?? "Not assigned")</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                                        <h6 class="fw-bold mb-0">Service Options (@service.ServiceOptions.Count)</h6>
                                                        <button class="btn btn-outline-primary btn-sm" onclick="addServiceOption(@service.Id)">
                                                            <i class="fas fa-plus me-1"></i>Add Option
                                                        </button>
                                                    </div>
                                                    @if (service.ServiceOptions.Any())
                                                    {
                                                        @foreach (var option in service.ServiceOptions.OrderBy(so => so.OptID))
                                                        {
                                                            <div class="border rounded p-3 mb-2">
                                                                <div class="d-flex justify-content-between align-items-start">
                                                                    <div class="flex-grow-1">
                                                                        <div class="fw-semibold">@option.OptName</div>
                                                                        <div class="text-muted small">@option.OptDesc</div>
                                                                        <div class="mt-1">
                                                                            <span class="badge bg-success">@option.OptCost?.ToString("C")</span>
                                                                            <span class="badge bg-info">@option.OptAvailability</span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="d-flex gap-1">
                                                                        <button class="btn btn-outline-primary btn-sm" onclick="editServiceOption(@option.Id)" title="Edit Option">
                                                                            <i class="fas fa-edit"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-success btn-sm" onclick="addServiceOptionFeature(@option.Id)" title="Add Feature">
                                                                            <i class="fas fa-plus"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteServiceOption(@option.Id)" title="Delete Option">
                                                                            <i class="fas fa-trash"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                @if (option.Features.Any())
                                                                {
                                                                    <div class="mt-3">
                                                                        <small class="text-muted fw-semibold">Features:</small>
                                                                        <div class="mt-1">
                                                                            @foreach (var feature in option.Features.OrderBy(f => f.FeatID))
                                                                            {
                                                                                <div class="d-flex justify-content-between align-items-center border-start border-3 border-info ps-2 py-1 mb-1">
                                                                                    <div class="flex-grow-1">
                                                                                        <small class="fw-semibold">@feature.FeatName</small>
                                                                                        <small class="text-muted d-block">@feature.FeatDesc</small>
                                                                                    </div>
                                                                                    <div class="d-flex gap-1">
                                                                                        <small class="badge bg-light text-dark">@feature.FeatCost?.ToString("C")</small>
                                                                                        <button class="btn btn-outline-primary btn-sm btn-xs" onclick="editServiceOptionFeature(@feature.Id)" title="Edit Feature">
                                                                                            <i class="fas fa-edit"></i>
                                                                                        </button>
                                                                                        <button class="btn btn-outline-danger btn-sm btn-xs" onclick="deleteServiceOptionFeature(@feature.Id)" title="Delete Feature">
                                                                                            <i class="fas fa-trash"></i>
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            }
                                                                        </div>
                                                                    </div>
                                                                }
                                                            </div>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <div class="text-center py-3 border rounded">
                                                            <small class="text-muted">No options available</small>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-server text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted">No services found</h5>
                            <p class="text-muted">Create your first service to get started.</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Create First Service
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" id="categoryModalContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<!-- Service Option Modal -->
<div class="modal fade" id="serviceOptionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" id="serviceOptionModalContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<!-- Service Option Feature Modal -->
<div class="modal fade" id="serviceOptionFeatureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" id="serviceOptionFeatureModalContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize DataTables
            $('#categoriesTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[0, "asc"]]
            });

            // Add hover effects to stat cards
            $('.admin-stat-card').hover(
                function() { $(this).addClass('shadow-lg'); },
                function() { $(this).removeClass('shadow-lg'); }
            );
        });

        // Category Management Functions
        function showCategoryModal() {
            $.get('@Url.Action("CreateCategory")')
                .done(function(data) {
                    $('#categoryModalContent').html(data);
                    $('#categoryModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading category form.');
                });
        }

        function editCategory(id) {
            $.get('@Url.Action("EditCategory")/' + id)
                .done(function(data) {
                    $('#categoryModalContent').html(data);
                    $('#categoryModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading category form.');
                });
        }

        function deleteCategory(id) {
            if (confirm('Are you sure you want to delete this category?')) {
                $.post('@Url.Action("DeleteCategory")', { id: id, __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val() })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Error deleting category.');
                    });
            }
        }

        // Service Option Management Functions
        function addServiceOption(serviceId) {
            $.get('@Url.Action("CreateServiceOption")/' + serviceId)
                .done(function(data) {
                    $('#serviceOptionModalContent').html(data);
                    $('#serviceOptionModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading service option form.');
                });
        }

        function editServiceOption(id) {
            $.get('@Url.Action("EditServiceOption")/' + id)
                .done(function(data) {
                    $('#serviceOptionModalContent').html(data);
                    $('#serviceOptionModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading service option form.');
                });
        }

        function deleteServiceOption(id) {
            if (confirm('Are you sure you want to delete this service option?')) {
                $.post('@Url.Action("DeleteServiceOption")', { id: id, __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val() })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Error deleting service option.');
                    });
            }
        }

        // Service Option Feature Management Functions
        function addServiceOptionFeature(serviceOptionId) {
            $.get('@Url.Action("CreateServiceOptionFeature")/' + serviceOptionId)
                .done(function(data) {
                    $('#serviceOptionFeatureModalContent').html(data);
                    $('#serviceOptionFeatureModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading service option feature form.');
                });
        }

        function editServiceOptionFeature(id) {
            $.get('@Url.Action("EditServiceOptionFeature")/' + id)
                .done(function(data) {
                    $('#serviceOptionFeatureModalContent').html(data);
                    $('#serviceOptionFeatureModal').modal('show');
                })
                .fail(function() {
                    alert('Error loading service option feature form.');
                });
        }

        function deleteServiceOptionFeature(id) {
            if (confirm('Are you sure you want to delete this service option feature?')) {
                $.post('@Url.Action("DeleteServiceOptionFeature")', { id: id, __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val() })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message);
                        }
                    })
                    .fail(function() {
                        alert('Error deleting service option feature.');
                    });
            }
        }

        function editService(id) {
            window.location.href = '@Url.Action("Edit")/' + id;
        }

        // Form submission handlers
        $(document).on('submit', '#categoryForm', function(e) {
            e.preventDefault();
            var form = $(this);
            var url = form.attr('action');
            var formData = form.serialize();

            $.post(url, formData)
                .done(function(response) {
                    if (response.success) {
                        $('#categoryModal').modal('hide');
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function() {
                    alert('Error submitting form.');
                });
        });

        $(document).on('submit', '#serviceOptionForm', function(e) {
            e.preventDefault();
            var form = $(this);
            var url = form.attr('action');
            var formData = form.serialize();

            $.post(url, formData)
                .done(function(response) {
                    if (response.success) {
                        $('#serviceOptionModal').modal('hide');
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function() {
                    alert('Error submitting form.');
                });
        });

        $(document).on('submit', '#serviceOptionFeatureForm', function(e) {
            e.preventDefault();
            var form = $(this);
            var url = form.attr('action');
            var formData = form.serialize();

            $.post(url, formData)
                .done(function(response) {
                    if (response.success) {
                        $('#serviceOptionFeatureModal').modal('hide');
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                })
                .fail(function() {
                    alert('Error submitting form.');
                });
        });
    </script>
}

<style>
    .btn-xs {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }

    .accordion-button:not(.collapsed) {
        background-color: #f8f9fa;
        border-color: #dee2e6;
    }

    .accordion-button:focus {
        box-shadow: none;
        border-color: #dee2e6;
    }
</style>
