using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class Category : BaseEntity
{
    [Required]
    public int CategID { get; set; }
    
    public int? ParentID { get; set; }
    
    [Required]
    [StringLength(50)]
    public string CategName { get; set; } = string.Empty;
    
    public string? CategDesc { get; set; }
    
    // Navigation properties
    public ICollection<Service> Services { get; set; } = new List<Service>();
    public Category? Parent { get; set; }
    public ICollection<Category> Children { get; set; } = new List<Category>();
}
